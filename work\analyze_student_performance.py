import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Load the data
data = pd.read_csv('student_habits_performance.csv')

# Check the data structure
print("Data Overview:")
print(data.head())
print("\nData Description:")
print(data.describe())

# Plot the distribution of student performance
plt.figure(figsize=(10, 6))
sns.histplot(data['exam_score'], bins=20, kde=True, color='skyblue')
plt.title('Distribution of Student Performance', fontproperties='assets/SimSun.ttf')
plt.xlabel('Exam Score', fontproperties='assets/SimSun.ttf')
plt.ylabel('Frequency', fontproperties='assets/SimSun.ttf')
plt.grid(True)
plt.savefig('student_performance_distribution.png')
plt.show()

print("Chart saved as 'student_performance_distribution.png'.")