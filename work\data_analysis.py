
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 设置字体
font_path = '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc' # 替换为你的SimSun.ttf字体路径
prop = fm.FontProperties(fname=font_path)

try:
    df = pd.read_csv('student_habits_performance.csv')
    print("数据读取成功！")
    print(df.info())
    print("\n数据描述性统计：")
    print(df.describe())
    # 处理缺失值，用均值填充数值型缺失值，众数填充类别型缺失值
    for col in df.columns:
        if df[col].dtype == 'object':
            df[col] = df[col].fillna(df[col].mode()[0])
        elif df[col].dtype in ['int64', 'float64']:
            df[col] = df[col].fillna(df[col].mean())
    print("\n处理缺失值后：")
    print(df.info())
    print(df.describe())

    # 保存处理后的数据
    df.to_csv('processed_student_habits_performance.csv', index=False)
    print("数据已保存到 processed_student_habits_performance.csv")

except FileNotFoundError:
    print("文件 'student_habits_performance.csv' 未找到！")
except pd.errors.EmptyDataError:
    print("文件 'student_habits_performance.csv' 为空！")
except pd.errors.ParserError:
    print("文件 'student_habits_performance.csv' 解析错误！")
except Exception as e:
    print(f"发生错误: {e}")

