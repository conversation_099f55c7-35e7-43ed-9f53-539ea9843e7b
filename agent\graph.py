# 从 langgraph.graph 模块导入 StateGraph 类、START 和 END 常量
# StateGraph 用于构建状态图，START 和 END 分别表示图的起始和结束节点
from langgraph.graph import StateGraph, START, END
# 从 langgraph.checkpoint.memory 模块导入 MemorySaver 类，用于保存和恢复图的状态
from langgraph.checkpoint.memory import MemorySaver
# 从 state 模块导入 State 类，用于管理图执行过程中的状态
from .state import State
# 从 nodes 模块导入各个节点对应的函数
from .nodes import (
    report_node,  # 报告生成节点函数
    execute_node,  # 步骤执行节点函数
    create_planner_node,  # 计划创建节点函数
    update_planner_node  # 计划更新节点函数
)


def _build_base_graph():
    """
    构建并返回包含所有节点和边的基础状态图。

    Returns:
        StateGraph: 配置好节点和边的状态图构建器实例。
    """
    # 创建一个 StateGraph 实例，使用 State 类来管理状态
    builder = StateGraph(State)
    # 添加一条从起始节点 START 到 'create_planner' 节点的边
    builder.add_edge(START, "create_planner")
    # 添加 'create_planner' 节点，并关联 create_planner_node 函数
    builder.add_node("create_planner", create_planner_node)
    # 添加 'update_planner' 节点，并关联 update_planner_node 函数
    builder.add_node("update_planner", update_planner_node) 
    # 添加 'execute' 节点，并关联 execute_node 函数
    builder.add_node("execute", execute_node)
    # 添加 'report' 节点，并关联 report_node 函数
    builder.add_node("report", report_node)
    # 添加一条从 'report' 节点到结束节点 END 的边
    builder.add_edge("report", END)
    return builder


def build_graph_with_memory():
    """
    构建并返回带有内存功能的智能代理工作流图。

    Returns:
        编译后的带有内存功能的工作流图。
    """
    # 创建一个 MemorySaver 实例，用于保存图的状态
    memory = MemorySaver()
    # 调用 _build_base_graph 函数构建基础状态图
    builder = _build_base_graph()
    # 编译状态图，并指定使用 memory 作为检查点保存器
    return builder.compile(checkpointer=memory)


def build_graph():
    """
    构建并返回不带有内存功能的智能代理工作流图。

    Returns:
        编译后的不带有内存功能的工作流图。
    """
    # 调用 _build_base_graph 函数构建基础状态图
    builder = _build_base_graph()
    # 编译状态图
    return builder.compile()


# 调用 build_graph 函数构建不带有内存功能的工作流图
graph = build_graph()

# 定义输入数据，包含用户消息、初始计划、观察信息和最终报告
inputs = {
    "user_message": "对所给文档进行分析，生成分析报告，文档路径为student_habits_performance.csv", 
    "plan": None,
    "observations": [], 
    "final_report": ""
}

# 调用图的 invoke 方法，传入输入数据和递归限制参数
graph.invoke(inputs, {"recursion_limit":100})
