# 从 langgraph.graph 模块导入 MessagesState 类，用于管理消息状态
from langgraph.graph import MessagesState
# 从 typing 模块导入类型注解，用于增强代码的可读性和可维护性
from typing import Optional, List, Dict, Literal

# 从 enum 模块导入 Enum 类，不过当前代码未实际使用该导入
from enum import Enum
# 再次从 typing 模块导入 List 和 Optional 类型注解，部分重复导入可移除
from typing import List, Optional

# 从 pydantic 模块导入 BaseModel 和 Field 类，BaseModel 用于定义数据模型，Field 用于字段验证和元数据定义
from pydantic import BaseModel, Field


class Step(BaseModel):
    """
    定义计划中单个步骤的数据模型。

    Attributes:
        title (str): 步骤的标题，默认为空字符串。
        description (str): 步骤的描述信息，默认为空字符串。
        status (Literal["pending", "completed"]): 步骤的状态，只能是 "pending"（待处理）或 "completed"（已完成），默认为 "pending"。
    """
    title: str = ""
    description: str = ""
    status: Literal["pending", "completed"] = "pending"


class Plan(BaseModel):
    """
    定义计划的数据模型。

    Attributes:
        goal (str): 计划的目标，默认为空字符串。
        thought (str): 关于计划的思考内容，默认为空字符串。
        steps (List[Step]): 计划包含的步骤列表，每个步骤是 Step 类的实例，默认为空列表。
    """
    goal: str = ""
    thought: str = ""
    steps: List[Step] = []

class State(MessagesState):
    """
    定义程序的状态数据模型，继承自 MessagesState 类。

    Attributes:
        user_message (str): 用户输入的消息，默认为空字符串。
        plan (Plan): 当前的计划，是 Plan 类的实例。
        observations (List): 观察到的信息列表，默认为空列表。
        final_report (str): 最终报告的内容，默认为空字符串。
    """
    user_message: str = ""
    plan: Plan
    observations: List = []
    final_report: str =  ""
