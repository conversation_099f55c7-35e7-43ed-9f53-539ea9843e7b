#!/usr/bin/env python3
# 文件路径: start_gradio.py
# 混合型智能代理Gradio界面启动脚本

import os
import sys
import subprocess
import time
from pathlib import Path

def check_dependencies():
    """检查必要的依赖是否已安装"""
    required_packages = [
        'gradio',
        'langchain',
        'langgraph', 
        'langchain_openai',
        'trio',
        'mcp',
        'pandas',
        'matplotlib',
        'seaborn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for pkg in missing_packages:
            print(f"   - {pkg}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_backend_service():
    """检查SearxNG后端服务是否运行"""
    try:
        import requests
        response = requests.get("http://localhost:8088", timeout=5)
        if response.status_code == 200:
            print("✅ SearxNG后端服务正在运行 (端口8088)")
            return True
    except:
        pass
    
    print("⚠️  SearxNG后端服务未运行")
    print("请按照以下步骤启动后端服务:")
    print("1. 进入 backend/searxng-docker-master 目录")
    print("2. 运行: docker compose up -d")
    return False

def check_api_keys():
    """检查API密钥配置"""
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv("DEEPSEEK_API_KEY") or os.getenv("OPENAI_API_KEY") or os.getenv("OPENROUTER_API_KEY")
    
    if api_key:
        print("✅ API密钥已配置")
        return True
    else:
        print("⚠️  未检测到API密钥")
        print("请在.env文件中配置以下任一密钥:")
        print("- DEEPSEEK_API_KEY")
        print("- OPENAI_API_KEY") 
        print("- OPENROUTER_API_KEY")
        return False

def check_work_directory():
    """检查并创建工作目录"""
    work_dir = Path("work")
    if not work_dir.exists():
        work_dir.mkdir()
        print("✅ 已创建工作目录: work/")
    else:
        print("✅ 工作目录已存在: work/")
    
    # 检查是否有示例数据文件
    csv_file = work_dir / "student_habits_performance.csv"
    if not csv_file.exists():
        # 尝试从根目录复制
        root_csv = Path("student_habits_performance.csv")
        if root_csv.exists():
            import shutil
            shutil.copy(root_csv, csv_file)
            print("✅ 已复制示例数据文件到工作目录")
        else:
            print("⚠️  未找到示例数据文件 student_habits_performance.csv")
    else:
        print("✅ 示例数据文件已存在")
    
    return True

def main():
    """主函数"""
    print("🚀 混合型智能代理 - Gradio界面启动检查")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    print()
    
    # 检查工作目录
    check_work_directory()
    
    print()
    
    # 检查API密钥
    api_ok = check_api_keys()
    
    print()
    
    # 检查后端服务
    backend_ok = check_backend_service()
    
    print()
    print("=" * 50)
    
    if not api_ok:
        print("⚠️  警告: API密钥未配置，某些功能可能无法使用")
    
    if not backend_ok:
        print("⚠️  警告: 后端服务未运行，网络搜索功能将无法使用")
    
    print("\n🎯 正在启动Gradio界面...")
    
    try:
        # 启动Gradio应用
        from gradio_app import create_gradio_interface
        
        demo = create_gradio_interface()
        
        print("\n✅ Gradio界面已启动!")
        print("🌐 访问地址: http://localhost:7860")
        print("📱 移动端访问: http://[您的IP]:7860")
        print("\n💡 使用提示:")
        print("1. 选择预定义任务或输入自定义任务")
        print("2. 点击'开始执行'启动任务")
        print("3. 在'执行结果'区域查看生成的报告")
        print("4. 在'生成文件'区域查看和预览生成的文件")
        print("\n按 Ctrl+C 停止服务")
        
        demo.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            debug=False,
            show_error=True,
            quiet=False
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("请检查错误信息并重试")
        sys.exit(1)

if __name__ == "__main__":
    main()
