import pandas as pd
import matplotlib.pyplot as plt
import os

# 设置中文字体
font_path = 'assets/SimSun.ttf'
plt.rcParams['font.family'] = 'SimSun'

# 读取数据
data = pd.read_csv('student_habits_performance.csv')
print("数据读取完成，前5行数据预览：")
print(data.head())

# 关键统计信息
print("\n关键统计信息：")
print(data.describe())

# 分析学习时长与成绩的关系
# 按学习时长分组计算平均成绩
study_hours_performance = data.groupby('study_hours_per_day')['exam_score'].mean().sort_values(ascending=False)
print("\n不同学习时长的平均成绩：")
print(study_hours_performance)

# 生成TOP10学习时长与成绩的柱状图
plt.figure(figsize=(10, 6))
study_hours_performance.head(10).plot(kind='bar', color='skyblue')
plt.title('TOP10学习时长与平均成绩关系')
plt.xlabel('学习时长 (小时/天)')
plt.ylabel('平均成绩')
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig('top10_study_hours_performance.png')
print("\nTOP10学习时长与成绩关系图已保存为top10_study_hours_performance.png")

# 分析睡眠时间与成绩的关系
# 按睡眠时间分组计算平均成绩
sleep_performance = data.groupby('sleep_hours')['exam_score'].mean().sort_values(ascending=False)
print("\n不同睡眠时间的平均成绩：")
print(sleep_performance)

# 生成TOP10睡眠时间与成绩的柱状图
plt.figure(figsize=(10, 6))
sleep_performance.head(10).plot(kind='bar', color='lightgreen')
plt.title('TOP10睡眠时间与平均成绩关系')
plt.xlabel('睡眠时间 (小时/天)')
plt.ylabel('平均成绩')
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig('top10_sleep_performance.png')
print("\nTOP10睡眠时间与成绩关系图已保存为top10_sleep_performance.png")