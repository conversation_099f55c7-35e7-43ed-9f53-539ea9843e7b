# 混合型智能代理项目依赖包

# 核心框架
langchain>=0.2.0
langgraph>=0.1.0
langchain-openai

# MCP协议支持
trio>=0.25.1
mcp>=0.2.0

# 数据处理
pandas>=1.5.0
numpy>=1.21.0
openpyxl>=3.0.0

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# Web界面
gradio>=4.0.0

# 图像处理
Pillow>=9.0.0

# 环境变量管理
python-dotenv>=0.19.0

# HTTP请求
requests>=2.28.0

# 日志处理
loguru>=0.6.0

# 文件处理
pathlib2>=2.3.0

# 数据验证
pydantic>=1.10.0

# 异步支持
asyncio>=3.4.3

# 科学计算
scipy>=1.9.0

# 统计分析
statsmodels>=0.13.0

# 机器学习 (可选)
scikit-learn>=1.1.0

# 自然语言处理 (可选)
nltk>=3.7

# 网络爬虫 (可选)
beautifulsoup4>=4.11.0
lxml>=4.9.0

# 数据库支持 (可选)
sqlalchemy>=1.4.0

# 缓存支持 (可选)
redis>=4.3.0

# 配置管理
configparser>=5.3.0

# 时间处理
python-dateutil>=2.8.0

# JSON处理
ujson>=5.4.0

# 压缩支持
zipfile36>=0.1.3

# 系统信息
psutil>=5.9.0
