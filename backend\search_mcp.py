# 尝试从 mcp.server.fastmcp 模块导入 FastMCP 类
from mcp.server.fastmcp import FastMCP
# 导入 requests 库，用于发送 HTTP 请求
import requests
# 从 openai 模块导入 OpenAI 类，用于调用 OpenAI API
from openai import OpenAI
# 创建 FastMCP 类的实例，传入参数 "search"
mcp = FastMCP("search")
# 重复导入 pymysql 库，此操作多余
import pymysql
# 导入 pandas 库，并使用别名 pd，用于数据处理和分析
import pandas as pd
# 从 prompts 模块导入所有内容
from prompts import *
# 导入 logging 模块，用于记录程序运行日志
import logging
import os
from dotenv import load_dotenv
# # 定义 OpenAI API 的基础 URL
# base_url = "https://openrouter.ai/api/v1"
# # 定义 OpenAI API 的密钥
# api_key = '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'
# # 定义使用的模型名称
# model_name = 'google/gemini-flash-1.5'
# # 创建 OpenAI 类的实例，传入基础 URL 和 API 密钥
# client = OpenAI(
#             base_url=base_url,
#             api_key=api_key,
#         )
# 定义使用的模型名称 (我们保留这个，因为它在这个文件中被多次使用)
model_name = 'deepseek-chat'

# 创建 OpenAI 类的实例，直接连接 DeepSeek
client = OpenAI(
    # 【关键修改】更改 base_url
    base_url="https://api.deepseek.com/v1",
    # 【关键修改】使用新的环境变量
    api_key=os.getenv("DEEPSEEK_API_KEY1")
)

# llm = ChatOpenAI(
#     # 我们继续使用这个已知能工作的模型
#     model="deepseek/deepseek-chat",
#     # OpenRouter API 地址
#     base_url='https://openrouter.ai/api/v1',
#     # 从环境变量中安全地读取 API 密钥
#     api_key=os.getenv("OPENROUTER_API_KEY"),
#     temperature=0.1,
#     max_tokens=2048,
#     # 添加 OpenRouter 推荐的 HTTP Headers，这对于避免某些错误至关重要
#     default_headers={
#         "HTTP-Referer": "http://localhost:8088/search", # 您的应用来源
#         "X-Title": "Hybrid Agent"           # 您的应用名称
#     }
# )

# 创建日志记录器，名称为当前模块名
logger = logging.getLogger(__name__)
# 设置日志记录器的日志级别为 INFO
logger.setLevel(logging.INFO)

# 创建控制台处理器，用于将日志输出到控制台
console_handler = logging.StreamHandler()
# 设置控制台处理器的日志级别为 INFO
console_handler.setLevel(logging.INFO)
# 定义控制台日志的输出格式
console_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# 为控制台处理器设置日志格式
console_handler.setFormatter(console_formatter)
# 将控制台处理器添加到日志记录器中
logger.addHandler(console_handler)

# 创建文件处理器，用于将日志写入 test.log 文件
file_handler = logging.FileHandler('test.log')
# 设置文件处理器的日志级别为 INFO
file_handler.setLevel(logging.INFO)
# 定义文件日志的输出格式
file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# 为文件处理器设置日志格式
file_handler.setFormatter(file_formatter)
# 将文件处理器添加到日志记录器中
logger.addHandler(file_handler)



# 定义生成搜索查询的函数，stream 参数未被使用
def generate_query(query, stream=False):
    # 定义系统提示词，要求生成最多四个精确的搜索查询
    prompt ="""You are an expert research assistant. Given the user's query, generate up to four distinct, precise search queries that would help gather comprehensive information on the topic.
    Return only a Python list of strings, for example: ['query1', 'query2', 'query3']."""
        
    # 调用 OpenAI API 创建聊天完成任务
    response = client.chat.completions.create(
                    model=model_name,
                    messages = [
        {"role": "system", "content": "You are a helpful and precise research assistant."},
        {"role": "user", "content": f"User Query: {query}\n\n{prompt}"}
    ]
                )
    # 返回 API 响应中的第一条消息内容
    return response.choices[0].message.content


# 定义判断网页内容是否有用的函数
def if_useful(query: str, page_text: str):
    # 定义系统提示词，要求判断网页内容是否与查询相关
    prompt ="""You are a critical research evaluator. Given the user's query and the content of a webpage, determine if the webpage contains information relevant and useful for addressing the query.
    Respond with exactly one word: 'Yes' if the page is useful, or 'No' if it is not. Do not include any extra text."""
    
    # 调用 OpenAI API 创建聊天完成任务
    response = client.chat.completions.create(
                    model=model_name,
                    messages = [
        {"role": "system", "content": "You are a strict and concise evaluator of research relevance."},
        {"role": "user", "content": f"User Query: {query}\n\nWebpage Content (first 20000 characters):\n{page_text[:20000]}\n\n{prompt}"}
    ]
                )
    
    # 获取 API 响应中的第一条消息内容
    response = response.choices[0].message.content
    
    if response:
        # 去除响应内容的首尾空白字符
        answer = response.strip()
        if answer in ["Yes", "No"]:
            return answer
        else:
            # 备用方案：尝试从响应中提取 Yes 或 No
            if "Yes" in answer:
                return "Yes"
            elif "No" in answer:
                return "No"
    return "No"

# 定义提取相关上下文的函数
def extract_relevant_context(query, search_query, page_text):
    # 定义系统提示词，要求提取与用户查询相关的网页内容
    prompt ="""You are an expert information extractor. Given the user's query, the search query that led to this page, and the webpage content, extract all pieces of information that are relevant to answering the user's query.
    Return only the relevant context as plain text without commentary."""
    
    # 调用 OpenAI API 创建聊天完成任务
    response = client.chat.completions.create(
                    model=model_name,
                    messages = [
        {"role": "system", "content": "You are an expert in extracting and summarizing relevant information."},
        {"role": "user", "content": f"User Query: {query}\nSearch Query: {search_query}\n\nWebpage Content (first 20000 characters):\n{page_text[:20000]}\n\n{prompt}"}
    ]
                )
    
    # 获取 API 响应中的第一条消息内容
    response = response.choices[0].message.content
    if response:
        # 去除响应内容的首尾空白字符并返回
        return response.strip()
    return ""

# 定义获取新搜索查询的函数
def get_new_search_queries(user_query, previous_search_queries, all_contexts):
    # 将所有提取的上下文组合成一个字符串
    context_combined = "\n".join(all_contexts)
    # 定义系统提示词，要求根据已有信息判断是否需要进一步研究并提供新的搜索查询
    prompt ="""You are an analytical research assistant. Based on the original query, the search queries performed so far, and the extracted contexts from webpages, determine if further research is needed.
    If further research is needed, provide up to four new search queries as a Python list (for example, ['new query1', 'new query2']). If you believe no further research is needed, respond with exactly .
    Output only a Python list or the token  without any additional text."""
    
    # 调用 OpenAI API 创建聊天完成任务
    response = client.chat.completions.create(
                    model=model_name,
                    messages = [
        {"role": "system", "content": "You are an expert in extracting and summarizing relevant information."},
        {"role": "user", "content": f"User Query: {user_query}\nPrevious Search Queries: {previous_search_queries}\n\nExtracted Relevant Contexts:\n{context_combined}\n\n{prompt}"}
    ]
                )
    
    # 获取 API 响应中的第一条消息内容
    response = response.choices[0].message.content
    if response:
        # 去除响应内容的首尾空白字符
        cleaned = response.strip()
        if cleaned == "":
            return ""
        try:
            # 使用 eval 函数将响应内容转换为 Python 列表
            new_queries = eval(cleaned)
            if isinstance(new_queries, list):
                return new_queries
            else:
                # 记录日志，提示 LLM 未返回列表
                logger.info(f"LLM did not return a list for new search queries. Response: {response}")
                return []
        except Exception as e:
            # 记录日志，提示解析新搜索查询时出错
            logger.error(f"Error parsing new search queries:{e}, Response:{response}")
            return []
    return []


# 重新组织此函数，集成 get_images 调用，并将 top_k 作为参数
def web_search(query: str, top_k: int = 2, categories: str = 'general') -> str:
    
    links = []
    # 发送 HTTP GET 请求，获取搜索结果
    response = requests.get(f'http://localhost:8088/search?format=json&q={query}&language=zh-CN&time_range=&safesearch=0&categories={categories}', timeout=10)
    results = response.json()['results']
    for result in results[:top_k]:
        # 根据不同的搜索类别，获取对应的链接
        links.append(result['url' if categories == 'general' else 'img_src' if categories == 'images' else ''])
    
    return links
    

# 定义获取网页文本内容的函数
def fetch_webpage_text(url):
    # 定义 Jina 的基础 URL
    JINA_BASE_URL = 'https://r.jina.ai/'
    # 拼接完整的 URL
    full_url = f"{JINA_BASE_URL}{url}"
    
    try:
        # 发送 HTTP GET 请求，获取网页内容
        resp = requests.get(full_url, timeout=50)
        if resp.status_code == 200:
            return resp.text
        else:
            # 记录日志，提示 Jina 获取网页内容出错
            text = resp.text
            logger.info(f"Jina fetch error for {url}: {resp.status_code} - {text}")
            return ""
    except Exception as e:
        # 记录日志，提示获取网页文本内容出错
        logger.error(f"Error fetching webpage text with Jina:{e}")
        return ""


# 定义处理链接的函数
def process_link(link, query, search_query):
    # 记录日志，提示正在获取链接的内容
    logger.info(f"Fetching content from: {link}")
    # 获取网页文本内容
    page_text = fetch_webpage_text(link)
    if not page_text:
        return None
    # 判断网页内容是否有用
    usefulness = if_useful(query, page_text)
    # 记录日志，提示网页内容的有用性
    logger.info(f"Page usefulness for {link}: {usefulness}")
    if usefulness == "Yes":
        # 提取与用户查询相关的上下文
        context = extract_relevant_context(query, search_query, page_text)
        if context:
            # 记录日志，提示提取的上下文内容
            logger.info(f"Extracted context from {link} (first 200 chars): {context[:200]}")
            return context
    return None


# 定义获取图片描述的函数
def get_images_description(iamge_url):
    # 调用 OpenAI API 创建聊天完成任务，使用多模态模型
    completion = client.chat.completions.create(
    
    model="qwen/qwen2.5-vl-32b-instruct:free",
    messages=[
        {
        "role": "user",
        "content": [
            {
            "type": "text",
            "text": "使用一句话描述图片的内容"
            },
            {
            "type": "image_url",
            "image_url": {
                "url": iamge_url
            }
            }
        ]
        }
    ]
    )
    # 返回 API 响应中的第一条消息内容
    return completion.choices[0].message.content

# 将 search 函数注册为 MCP 工具
@mcp.tool()
def search(query: str) -> str:
    """互联网搜索"""
    # 定义迭代次数上限
    iteration_limit = 3
    # 初始化迭代次数
    iteration = 0
    # 初始化聚合的上下文列表
    aggregated_contexts = []  
    # 初始化所有搜索查询列表
    all_search_queries = []   
    iteration = 0
    
    # 生成新的搜索查询并转换为 Python 列表
    new_search_queries = eval(generate_query(query))
    # 将新的搜索查询添加到所有搜索查询列表中
    all_search_queries.extend(new_search_queries)
    # 如果原始查询不在搜索查询列表中，将其添加进去
    if query not in all_search_queries:
        all_search_queries.append(query)
    while iteration < iteration_limit:
            # 记录日志，提示当前迭代次数
            logger.info(f"\n=== Iteration {iteration + 1} ===")
            # 初始化当前迭代的上下文列表
            iteration_contexts = []
            # 对每个新的搜索查询进行网页搜索
            search_results = [web_search(query, top_k=2, categories='general') for query in new_search_queries]

            # 初始化唯一链接字典
            unique_links = {}
            for idx, links in enumerate(search_results):
                # 获取当前搜索查询
                search_query = new_search_queries[idx]  # prevent the query being replaced as the Search parameter
                for link in links:
                    if link not in unique_links:
                        unique_links[link] = search_query

            # 记录日志，提示当前迭代聚合的唯一链接数量
            logger.info(f"Aggregated {len(unique_links)} unique links from this iteration.")

            # 处理每个链接：获取内容、判断有用性、提取上下文
            link_results = [
                process_link(link, query, unique_links[link])
                for link in unique_links
            ]
            
            # 收集非 None 的上下文
            for res in link_results:
                if res:
                    iteration_contexts.append(res)

            if iteration_contexts:
                # 将当前迭代的上下文添加到聚合的上下文列表中
                aggregated_contexts.extend(iteration_contexts)
            else:
                # 记录日志，提示当前迭代未找到有用的上下文
                logger.info("No useful contexts were found in this iteration.")

            # 获取新的搜索查询
            new_search_queries = get_new_search_queries(query, all_search_queries, aggregated_contexts)
            if new_search_queries == "":
                # 记录日志，提示 LLM 表示不需要进一步研究
                logger.info("LLM indicated that no further research is needed.")
                break
            elif new_search_queries:
                # 记录日志，提示 LLM 提供了新的搜索查询
                logger.info(f"LLM provided new search queries:{new_search_queries}")
                all_search_queries.extend(new_search_queries)
            else:
                # 记录日志，提示 LLM 未提供新的搜索查询，结束循环
                logger.info("LLM did not provide any new search queries. Ending the loop.")
                break

            iteration += 1
    # 将聚合的上下文用换行符连接并返回
    return '\n\n'.join(aggregated_contexts)

# 将 get_images 函数注册为 MCP 工具
@mcp.tool()
def get_images(query: str) -> str:
    '''获取图片链接和描述'''
    # 记录日志，提示正在搜索图片
    logger.info(f"Searching for images for query: {query}")
    # 通过 web_search 函数获取图片链接
    img_srcs = web_search(query, top_k=2, categories='images')

    result = {}
    
    for img_src in img_srcs:
        # 记录日志，提示正在获取图片描述
        logger.info(f"Fetching image description for: {img_src}")
        # 获取图片描述
        description = get_images_description(img_src)
        # 记录日志，提示图片描述内容
        logger.info(f"Image description for {img_src}: {description}")
        result[img_src] = description
        
    return result
    
  

if __name__ == "__main__":
    # 运行 MCP 服务
    mcp.run()
