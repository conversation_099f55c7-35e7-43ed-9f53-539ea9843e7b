import pandas as pd

# 读取数据
data = pd.read_csv('student_habits_performance.csv')

# 检查缺失值
print("缺失值统计:")
print(data.isnull().sum())

# 处理缺失值
# 对于数值型列，用中位数填充；对于非数值型列，用众数填充
for column in data.columns:
    if data[column].dtype == 'object':
        data[column].fillna(data[column].mode()[0], inplace=True)
    else:
        data[column].fillna(data[column].median(), inplace=True)

# 检查异常值
print("\n异常值检查 (数值型列):")
numeric_cols = data.select_dtypes(include=['int64', 'float64']).columns
for column in numeric_cols:
    q1 = data[column].quantile(0.25)
    q3 = data[column].quantile(0.75)
    iqr = q3 - q1
    lower_bound = q1 - 1.5 * iqr
    upper_bound = q3 + 1.5 * iqr
    outliers = data[(data[column] < lower_bound) | (data[column] > upper_bound)]
    print(f"{column}: {len(outliers)} 个异常值")

# 处理异常值
# 将异常值替换为边界值
for column in numeric_cols:
    q1 = data[column].quantile(0.25)
    q3 = data[column].quantile(0.75)
    iqr = q3 - q1
    lower_bound = q1 - 1.5 * iqr
    upper_bound = q3 + 1.5 * iqr
    data[column] = data[column].clip(lower_bound, upper_bound)

# 检查重复值
print("\n重复值统计:")
print(f"{data.duplicated().sum()} 个重复值")

# 删除重复值
data.drop_duplicates(inplace=True)

# 转换数据类型
# 假设 'date' 列需要转换为日期类型
if 'date' in data.columns:
    data['date'] = pd.to_datetime(data['date'])

# 保存清洗后的数据
data.to_csv('cleaned_student_habits_performance.csv', index=False)
print("\n数据清洗完成，已保存为 cleaned_student_habits_performance.csv")