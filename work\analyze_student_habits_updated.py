import pandas as pd
import matplotlib.pyplot as plt
import os

# 设置中文字体
font_path = "assets/SimSun.ttf"
plt.rcParams['font.sans-serif'] = ['SimSun']  # 指定默认字体
plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像时负号'-'显示为方块的问题

# 读取数据
data = pd.read_csv("student_habits_performance.csv")
print("数据读取完成，前5行数据如下：")
print(data.head())

# 分析学习时间与成绩的关系
# 按学习时间分组，计算平均成绩
study_hours_avg = data.groupby('study_hours_per_day')['exam_score'].mean().sort_values(ascending=False)
print("\n按学习时间分组的平均成绩：")
print(study_hours_avg)

# 绘制TOP10学习时间与成绩的关系图
plt.figure(figsize=(10, 6))
study_hours_avg.head(10).plot(kind='bar', color='skyblue')
plt.title('TOP10 学习时间与平均成绩关系')
plt.xlabel('学习时间（小时/天）')
plt.ylabel('平均成绩')
plt.savefig('study_hours_performance.png')
print("\n图表已保存为 study_hours_performance.png")

# 分析睡眠时间与成绩的关系
# 按睡眠时间分组，计算平均成绩
sleep_hours_avg = data.groupby('sleep_hours')['exam_score'].mean().sort_values(ascending=False)
print("\n按睡眠时间分组的平均成绩：")
print(sleep_hours_avg)

# 绘制TOP10睡眠时间与成绩的关系图
plt.figure(figsize=(10, 6))
sleep_hours_avg.head(10).plot(kind='bar', color='lightgreen')
plt.title('TOP10 睡眠时间与平均成绩关系')
plt.xlabel('睡眠时间（小时）')
plt.ylabel('平均成绩')
plt.savefig('sleep_hours_performance.png')
print("\n图表已保存为 sleep_hours_performance.png")

# 分析饮食习惯与成绩的关系
# 按饮食习惯分组，计算平均成绩
diet_avg = data.groupby('diet_quality')['exam_score'].mean().sort_values(ascending=False)
print("\n按饮食习惯分组的平均成绩：")
print(diet_avg)

# 绘制TOP10饮食习惯与成绩的关系图
plt.figure(figsize=(10, 6))
diet_avg.head(10).plot(kind='bar', color='salmon')
plt.title('TOP10 饮食习惯与平均成绩关系')
plt.xlabel('饮食习惯评分')
plt.ylabel('平均成绩')
plt.savefig('diet_quality_performance.png')
print("\n图表已保存为 diet_quality_performance.png")