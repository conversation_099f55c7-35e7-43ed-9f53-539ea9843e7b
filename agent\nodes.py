# 文件路径: hybrid_agent/agent/nodes.py (最终修正版)

import json
import logging
from typing import Literal
from langchain_core.messages import AIMessage, HumanMessage,  SystemMessage, ToolMessage
from langgraph.types import Command
from langchain_openai import ChatOpenAI
from .state import State
from .prompts import *
from .tools import *
# 导入用于加载环境变量的库
from dotenv import load_dotenv
import os

# 在程序开始时加载 .env 文件中的环境变量
load_dotenv()

# 初始化 ChatOpenAI 实例
# llm = ChatOpenAI(
#     # 我们继续使用这个已知能工作的模型
#     model="deepseek/deepseek-chat",
#     # OpenRouter API 地址
#     base_url='https://openrouter.ai/api/v1',
#     # 从环境变量中安全地读取 API 密钥
#     api_key=os.getenv("OPENROUTER_API_KEY"),
#     temperature=0.1,
#     max_tokens=2048,
#     # 添加 OpenRouter 推荐的 HTTP Headers，这对于避免某些错误至关重要
#     default_headers={
#         "HTTP-Referer": "http://localhost:8088/search", # 您的应用来源
#         "X-Title": "Hybrid Agent"           # 您的应用名称
#     }
# )
llm = ChatOpenAI(
    # 指定要使用的 DeepSeek 模型
    model="deepseek-chat",  
    #  【关键修改】将 base_url 指向 DeepSeek 官方服务器
    base_url="https://api.deepseek.com/v1",
    #  【关键修改】从环境变量中读取新的 DEEPSEEK_API_KEY
    api_key=os.getenv("DEEPSEEK_API_KEY"),
    temperature=0.1,
    max_tokens=2048 # DeepSeek的免费额度很慷慨，我们可以把max_tokens调回去
)
# 设置日志
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
hander = logging.StreamHandler()
hander.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
hander.setFormatter(formatter)
logger.addHandler(hander)

def extract_json(text):
    """从文本中提取 JSON 格式的内容。"""
    if '```json' not in text:
        return text
    text = text.split('```json')[1].split('```')[0].strip()
    return text

def extract_answer(text):
    """从文本中提取最终答案。"""
    if '</think>' in text:
        answer = text.split("</think>")[-1]
        return answer.strip()
    return text

def create_planner_node(state: State):
    """创建计划节点，根据用户消息生成执行计划。"""
    logger.info("***正在运行Create Planner node***")
    messages = [SystemMessage(content=PLAN_SYSTEM_PROMPT), HumanMessage(content=PLAN_CREATE_PROMPT.format(user_message = state['user_message']))]
    
    while True:
        try:
            response = llm.invoke(messages)
            plan = json.loads(extract_json(extract_answer(response.content)))
            state['messages'] += [AIMessage(content=json.dumps(plan, ensure_ascii=False))]
            return Command(goto="execute", update={"plan": plan})
        except (json.JSONDecodeError, KeyError) as e:
            logger.warning(f"JSON解析失败，要求LLM修正。错误: {e}")
            error_message = f"Your previous response resulted in a JSON parsing error: {e}. Please strictly follow the format instructions and provide a valid JSON response without any additional commentary."
            messages.append(AIMessage(content=str(e))) # 将错误的原始AI响应加入
            messages.append(HumanMessage(content=error_message))

def update_planner_node(state: State):
    """更新计划节点，根据当前计划和目标更新执行计划。"""
    logger.info("***正在运行Update Planner node***")
    plan = state['plan']
    goal = plan['goal']
    # 将之前的观察结果也加入到上下文中，让规划者知道发生了什么
    context = "\n".join([str(obs.content) for obs in state.get('observations', [])])
    prompt = UPDATE_PLAN_PROMPT.format(plan=plan, goal=goal, context=context)
    
    messages = [SystemMessage(content=PLAN_SYSTEM_PROMPT), HumanMessage(content=prompt)]
    
    while True:
        try:
            response = llm.invoke(messages)
            plan = json.loads(extract_json(extract_answer(response.content)))
            state['messages']+=[AIMessage(content=json.dumps(plan, ensure_ascii=False))]
            return Command(goto="execute", update={"plan": plan})
        except Exception as e:
            logger.warning(f"更新计划时JSON解析失败，要求LLM修正。错误: {e}")
            messages.append(AIMessage(content=str(e)))
            messages.append(HumanMessage(content=f"json格式错误:{e}，请修正。"))
            
def execute_node(state: State):
    """执行节点，根据计划执行相应步骤。"""
    logger.info("***正在运行execute_node***")

    plan = state['plan']
    steps = plan['steps']
    current_step = None
    current_step_index = -1
    
    for i, step in enumerate(steps):
        if step.get('status', 'completed') == 'pending':
            current_step = step
            current_step_index = i
            break
            
    if current_step is None:
        logger.info("所有步骤已完成，准备生成报告。")
        return Command(goto='report')

    logger.info(f"当前执行STEP: {current_step}")
    
    messages = state.get('observations', []) + [
        SystemMessage(content=EXECUTE_SYSTEM_PROMPT),
        HumanMessage(content=EXECUTION_PROMPT.format(user_message=state['user_message'], step=current_step['description']))
    ]
    
    # 这是我们的项目与参考项目融合的地方：我们使用所有可用的工具
    all_tools = [internet_search, find_images, create_file, shell_exec]
    
    # 采用参考项目的多次工具调用循环，直到AI认为可以结束
    while True:
        response = llm.bind_tools(all_tools).invoke(messages)
        
        if not response.tool_calls:
            # 如果AI不再调用工具，说明它认为这个步骤完成了
            break

        # 处理工具调用
        for tool_call in response.tool_calls:
            tool_name = tool_call['name']
            tool_args = tool_call['args']
            selected_tool = next((t for t in all_tools if t.name == tool_name), None)
            
            if selected_tool:
                tool_result = selected_tool.invoke(tool_args)
                logger.info(f"工具'{tool_name}'调用，参数: {tool_args}\n结果: {tool_result}")
                # 将AI的工具调用意图和工具的实际执行结果都加入到消息历史中，用于下一轮循环
                messages.append(response) # 加入AI的回复
                messages.append(ToolMessage(content=json.dumps(tool_result, ensure_ascii=False), tool_call_id=tool_call['id']))
            else:
                logger.error(f"未找到工具: {tool_name}")
                messages.append(ToolMessage(content=f"错误: 未找到工具 {tool_name}", tool_call_id=tool_call['id']))

    # 循环结束后，认为当前步骤完成
    plan['steps'][current_step_index]['status'] = 'completed'
    
    summary = response.content
    logger.info(f"当前STEP执行总结: {summary}")
    
    # 我们只将最终的总结加入到messages中给planner看，不污染observations
    state['messages'].append(AIMessage(content=summary))
    # 将整个执行过程的对话历史（包含多轮工具调用）作为这一步的观察结果
    state['observations'] = messages

    return Command(goto='update_planner', update={'plan': plan})
    
def report_node(state: State):
    """报告节点，生成最终报告。"""
    logger.info("***正在运行report_node***")
    
    # 获取所有观察到的信息作为报告的上下文
    context = "\n---\n".join([str(msg.content) for msg in state.get("observations", [])])
    final_prompt = f"这是任务执行过程中的所有观察记录:\n{context}\n\n请基于以上所有信息，生成最终的综合报告。"
    
    messages = [SystemMessage(content=REPORT_SYSTEM_PROMPT), HumanMessage(content=final_prompt)]
    
    response = llm.invoke(messages)

    return {"final_report": response.content}