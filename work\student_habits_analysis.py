import pandas as pd
import matplotlib.pyplot as plt
import os

# Load data
data = pd.read_csv('student_habits_performance.csv')

# Display basic info
print("Data Overview:")
print(data.info())
print("\nDescriptive Statistics:")
print(data.describe())

# Analyze sleep duration vs performance
sleep_performance = data[['sleep_hours', 'exam_score']].corr()
print("\nCorrelation between Sleep Duration and Exam Score:")
print(sleep_performance)

# Analyze social media usage vs performance
social_media_performance = data[['social_media_hours', 'exam_score']].corr()
print("\nCorrelation between Social Media Usage and Exam Score:")
print(social_media_performance)

# Plot TOP10 students by exam score (sorted by exam_score in descending order)
top10_students = data.sort_values(by='exam_score', ascending=False).head(10)
plt.figure(figsize=(10, 6))
plt.bar(top10_students['student_id'], top10_students['exam_score'], color='skyblue')
plt.xlabel('Student ID', fontproperties='SimSun')
plt.ylabel('Exam Score', fontproperties='SimSun')
plt.title('TOP10 Students by Exam Score', fontproperties='SimSun')
plt.xticks(rotation=45)
plt.savefig('top10_students.png')
plt.close()

print("\nTOP10 Students by Exam Score chart saved as 'top10_students.png'.")