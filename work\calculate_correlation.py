import pandas as pd
import matplotlib.pyplot as plt

# 读取数据文件
data = pd.read_csv('student_habits_performance.csv')

# 检查并排除非数值列
numeric_data = data.select_dtypes(include=['float64', 'int64'])

# 计算相关系数矩阵
correlation_matrix = numeric_data.corr()

# 打印相关系数矩阵
print("相关系数矩阵：")
print(correlation_matrix)

# 识别强相关关系（绝对值大于0.5）
strong_correlations = correlation_matrix[abs(correlation_matrix) > 0.5]
print("\n强相关关系（绝对值大于0.5）：")
print(strong_correlations)

# 绘制相关系数矩阵热力图
plt.figure(figsize=(10, 8))
plt.imshow(correlation_matrix, cmap='coolwarm', interpolation='none')
plt.colorbar()
plt.xticks(range(len(correlation_matrix.columns)), correlation_matrix.columns, rotation=90)
plt.yticks(range(len(correlation_matrix.columns)), correlation_matrix.columns)
plt.title('相关系数矩阵热力图')
plt.savefig('correlation_heatmap.png')
plt.show()