import pandas as pd
import matplotlib.pyplot as plt
import os

# Load the data
data = pd.read_csv('student_habits_performance.csv')

# Explore the relationship between sleep hours and exam scores
# Sort by sleep hours to identify patterns
sleep_sorted = data.sort_values(by='sleep_hours', ascending=False)
print("Top 10 students by sleep hours:")
print(sleep_sorted[['sleep_hours', 'exam_score']].head(10))

# Explore the relationship between social media usage and exam scores
# Sort by social media hours to identify patterns
social_media_sorted = data.sort_values(by='social_media_hours', ascending=False)
print("Top 10 students by social media usage:")
print(social_media_sorted[['social_media_hours', 'exam_score']].head(10))

# Generate a scatter plot for sleep hours vs. exam score
plt.figure(figsize=(10, 6))
plt.scatter(data['sleep_hours'], data['exam_score'], alpha=0.5)
plt.title('Relationship Between Sleep Hours and Exam Scores')
plt.xlabel('Sleep Hours')
plt.ylabel('Exam Score')
plt.grid(True)
plt.savefig('sleep_vs_score.png')
print("Scatter plot saved as 'sleep_vs_score.png'")

# Generate a scatter plot for social media usage vs. exam score
plt.figure(figsize=(10, 6))
plt.scatter(data['social_media_hours'], data['exam_score'], alpha=0.5)
plt.title('Relationship Between Social Media Usage and Exam Scores')
plt.xlabel('Social Media Usage (hours)')
plt.ylabel('Exam Score')
plt.grid(True)
plt.savefig('social_media_vs_score.png')
print("Scatter plot saved as 'social_media_vs_score.png'")
