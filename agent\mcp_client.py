# 这是最终正确版本的 mcp_client.py
import json
import trio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# 定义后端服务器脚本的路径
SERVER_SCRIPT_PATH = "./backend/search_mcp.py"

def _run_sync_tool_call(tool_name: str, tool_args: dict) -> str:
    """
    在一个同步函数中，运行一个异步的trio事件循环来启动stdio服务器并调用远程工具。
    """
    async def main():
        # 定义服务器参数，客户端将使用此命令启动一个后端的Python进程
        server_params = StdioServerParameters(command="python3", args=[SERVER_SCRIPT_PATH])
        try:
            # stdio_client 会自动启动和关闭服务器进程
            async with stdio_client(server_params) as (reader, writer):
                async with ClientSession(reader, writer) as session:
                    await session.initialize()
                    result = await session.call_tool(tool_name, tool_args)
                    if result and result.content and result.content[0].text:
                        return result.content[0].text
                    return "No content returned from tool."
        except Exception as e:
            print(f"MCP StdioClient Error: Failed to call tool '{tool_name}'. Error: {e}")
            raise

    try:
        # 使用 trio.run 来同步地运行异步的 main 函数
        result = trio.run(main)
        return json.dumps({"result": result}, ensure_ascii=False)
    except Exception as e:
        return json.dumps({"error": f"Failed to execute tool call for '{tool_name}': {e}"}, ensure_ascii=False)

def search_internet(query: str) -> str:
    """调用后端的搜索工具"""
    return _run_sync_tool_call("search", {"query": query})

def get_images_from_internet(query: str) -> str:
    """调用后端的图片获取工具"""
    return _run_sync_tool_call("get_images", {"query": query})
# # 文件路径: hybrid_agent/agent/mcp_client.py
# import subprocess
# import time
# import json
# from mcp import ClientSession, StdioServerParameters
# from mcp.client.stdio import stdio_client
# import trio  # 使用trio来运行异步的mcp客户端

# class MCPClient:
#     """
#     一个同步的包装器，用于与异步的MCP服务器进行交互。
#     """
#     def __init__(self, server_script_path: str):
#         self.server_script_path = server_script_path
#         self._server_process = None
#         self._client_session = None
#         self._exit_stack = None

#         # 启动服务器
#         self._server_process = subprocess.Popen(
#             ["python", self.server_script_path],
#             stdout=subprocess.PIPE,
#             stdin=subprocess.PIPE,
#         )
#         # 等待服务器启动
#         time.sleep(5)

#         # 在trio中初始化客户端
#         trio.run(self._initialize_client)

#     async def _initialize_client(self):
#         """异步初始化MCP客户端会话"""
#         server_params = StdioServerParameters(command="python", args=[self.server_script_path])
#         # 注意：此处我们不使用stdio_client上下文管理器，因为我们需要手动管理生命周期
#         # 这是对原始异步代码的简化和调整，以适应同步调用
#         # 实际上，更稳健的做法是保持异步或使用线程
#         # 但为了演示清晰，我们采用一个简化的trio.run方法
#         # 在实际生产中，你可能需要一个更复杂的IPC机制
#         pass # 实际连接逻辑在调用时处理

#     def _run_async_call(self, async_fn, *args):
#         """在一个新的trio事件循环中运行一个异步函数"""
#         async def main():
#             server_params = StdioServerParameters(
#                 command="python",
#                 args=[self.server_script_path],
#             )
#             async with stdio_client(server_params) as (reader, writer):
#                 async with ClientSession(reader, writer) as session:
#                     await session.initialize()
#                     return await async_fn(session, *args)
#         return trio.run(main)

#     def call_tool(self, tool_name: str, tool_args: dict) -> str:
#         """同步调用远程工具"""
#         async def _call(session, name, args):
#             result = await session.call_tool(name, args)
#             if result and result.content:
#                 # 假设结果在content[0].text中
#                 return result.content[0].text
#             return "No content returned from tool."

#         try:
#             result = self._run_async_call(_call, tool_name, tool_args)
#             return json.dumps({"result": result}, ensure_ascii=False)
#         except Exception as e:
#             return json.dumps({"error": f"Failed to call tool {tool_name}: {e}"}, ensure_ascii=False)

# # 在模块加载时创建一个客户端实例
# # 注意：这假设后端服务器在整个代理生命周期内是持续运行的
# mcp_client = MCPClient(server_script_path="./backend/search_mcp.py")

# def search_internet(query: str) -> str:
#     """调用后端的搜索工具"""
#     return mcp_client.call_tool("search", {"query": query})

# def get_images_from_internet(query: str) -> str:
#     """调用后端的图片获取工具"""
#     return mcp_client.call_tool("get_images", {"query": query})