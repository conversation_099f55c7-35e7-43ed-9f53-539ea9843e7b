# 文件路径: run.py (最终生产级版本)

import os
import shutil
import uuid
from agent.graph import build_graph
from langchain_core.messages import HumanMessage

# --- 1. 初始化和文件准备 ---
WORK_DIR = "work"
if not os.path.exists(WORK_DIR):
    os.makedirs(WORK_DIR)

# 这是一个示例，确保工作目录里有数据文件。
# 您可以根据自己的项目结构调整源文件路径。
# 假设源文件在项目根目录
source_csv_path = "student_habits_performance.csv" 
target_csv_path = os.path.join(WORK_DIR, "student_habits_performance.csv")
# 仅在工作目录中不存在时才复制
if os.path.exists(source_csv_path) and not os.path.exists(target_csv_path):
    shutil.copy(source_csv_path, target_csv_path)

# --- 2. 获取编译好的图实例 ---
graph = build_graph()

# --- 3. 定义初始输入 ---
# 使用我们之前优化过的、更明确的指令
complex_query = """
作为一个混合分析机器人，你的核心任务是深度结合**本地数据分析**与**最新的网络研究**，完成一份高质量的综合性分析报告。

**你必须严格按照以下步骤执行，缺一不可：**

1.  **本地数据分析**: 
    * 对工作目录下的 `student_habits_performance.csv` 文件进行深入的探索性数据分析(EDA)。
    * 分析关键变量（如睡眠时长、社交媒体使用时长）与考试成绩的关系。
    * 生成至少一个能体现核心发现的可视化图表，并保存为图片文件。

2.  **网络深度研究 (强制执行步骤)**:
    * **必须使用 `internet_search` 工具**，查找并整合至少两篇关于“青少年睡眠模式、数字媒体使用与学业成绩关联的最新（2024-2025年）权威研究或报告”。
    * **必须使用 `find_images` 工具**，查找一张能够宏观展示“数字分心”（Digital Distraction）或“健康作息”概念的示意图。

3.  **综合报告撰写 (必须结合网络研究成果)**:
    * 生成一份名为 `final_hybrid_report.md` 的最终报告。
    * 报告中，**必须将你在第1步的本地数据分析发现，与第2步的网络研究发现进行对比、印证或补充**。
    * 报告中**必须同时插入**你在本地生成的分析图表和你从网上找到的概念示意图，并对所有图表进行解释。
"""
inputs = { "user_message": complex_query }

# --- 4. 运行代理并健壮地处理输出 ---
# 使用唯一的ID来跟踪这次运行
config = {"recursion_limit": 150}
final_report = ""

# 流式执行图，并迭代处理每个事件
for event in graph.stream(inputs, config=config):
    # event 是一个字典，key是节点名，value是该节点的输出
    for key, value in event.items():
        # 【关键修改】增加一个判断，如果value是空的或不是字典，就跳过
        if not value or not isinstance(value, dict):
            continue

        print(f"节点: {key}")
        print("---")
        print(value)

        # 捕获并保存最终报告
        if key == "report" and 'final_report' in value:
            final_report = value['final_report']
            report_path = os.path.join(WORK_DIR, "final_analysis_report.md")
            try:
                with open(report_path, "w", encoding='utf-8') as f:
                    f.write(final_report)
                print(f"\n报告已生成并保存到: {report_path}")
            except Exception as e:
                print(f"\n保存报告时出错: {e}")

    print("\n======================================\n")

# --- 5. 在所有流程结束后，清晰地展示最终成果 ---
if final_report:
    print("\n\n\n--- [任务完成，最终报告如下] ---\n\n")
    print(final_report)
else:
    print("\n\n\n--- [任务完成，但未能捕获到最终报告] ---\n")