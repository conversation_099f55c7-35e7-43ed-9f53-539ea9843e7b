# 参考 https://docs.searxng.org/admin/settings/settings.html#settings-use-default-settings
# 是否使用默认设置，设置为 true 表示使用默认设置
use_default_settings: true

# 搜索引擎配置部分，定义要启用或禁用的搜索引擎
engines:
  # 启用默认禁用的引擎
  # 配置 Bing 搜索引擎，将其禁用状态设置为 false 以启用
  - name: bing
    disabled: false

  # 配置 360 搜索引擎，设置引擎名称、标识和启用状态
  - name: 360search
    engine: 360search
    shortcut: 360so
    disabled: false
  
  # 配置百度搜索引擎，设置引擎名称、标识和启用状态
  - name: baidu
    engine: baidu
    shortcut: baidu
    disabled: false
  
  # 配置爱奇艺搜索引擎，设置引擎名称、标识和启用状态
  - name: iqiyi
    engine: iqiyi
    shortcut: iq
    disabled: false

  # 配置 AcFun 搜索引擎，设置引擎名称、标识和启用状态
  - name: acfun
    engine: acfun
    shortcut: acf
    disabled: false

  # 禁用默认启用的引擎
  # 禁用 Arch Linux 维基百科搜索引擎
  - name: arch linux wiki
    engine: archlinux
    disabled: true
  # 禁用 DuckDuckGo 搜索引擎，此处存在拼写错误，正确应为 disabled
  - name: duckduckgo
    engine: duckduckgo
    distabled: true
  # 禁用 GitHub 搜索引擎
  - name: github
    engine: github
    shortcut: gh
    disabled: true
  # 禁用维基百科搜索引擎
  - name: wikipedia
    engine: wikipedia
    disabled: true

# 服务器配置部分
server:
  # base_url 定义在 SEARXNG_BASE_URL 环境变量中，参考 .env 和 docker-compose.yml 文件
  # 服务器的密钥，用于安全相关操作，建议修改此值
  secret_key: "888"  # change this!
  # 速率限制功能，对于私有实例可以禁用
  limiter: false  # can be disabled for a private instance
  # 是否启用图片代理功能
  image_proxy: true

# 用户界面配置部分
ui:
  # 是否在静态资源 URL 中使用哈希值，用于缓存控制
  static_use_hash: true

# Redis 配置部分，指定 Redis 服务的连接地址
redis:
  url: redis://redis:6379/0

# 搜索配置部分
search:
  # 安全搜索级别，0 表示不启用安全搜索
  safe_search: 0
  # 自动补全功能使用的引擎，留空表示不启用
  autocomplete: ""
  # 默认搜索语言，留空表示使用系统默认语言
  default_lang: ""
  # 支持的搜索结果格式
  formats:
    - html
    - json
    - csv
    - rss

# 速率限制配置部分
ratelimit:
  # 是否启用速率限制功能
  enabled: true
  # 调整每秒允许的请求数
  per_second: 5  
  # 调整每分钟允许的请求数
  per_minute: 60
