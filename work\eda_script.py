import pandas as pd
import matplotlib.pyplot as plt

# 读取数据文件
data = pd.read_csv('student_habits_performance.csv')

# 打印数据前几行以检查
print("数据预览:")
print(data.head())

# 检查数据的基本统计信息
print("\n基本统计信息:")
print(data.describe())

# 检查缺失值
print("\n缺失值统计:")
print(data.isnull().sum())

# 分析睡眠时长和社交媒体时长对考试分数的影响
plt.figure(figsize=(10, 6))
plt.scatter(data['sleep_hours'], data['exam_score'], color='blue', label='Sleep Hours vs Exam Score')
plt.scatter(data['social_media_hours'], data['exam_score'], color='red', label='Social Media Hours vs Exam Score')
plt.xlabel('Hours')
plt.ylabel('Exam Score')
plt.title('Impact of Sleep and Social Media on Exam Scores')
plt.legend()
plt.grid(True)
plt.savefig('sleep_social_vs_score.png')
plt.show()

print("图表已保存为 'sleep_social_vs_score.png'。")