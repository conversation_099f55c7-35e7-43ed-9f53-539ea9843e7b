import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Load the data
data = pd.read_csv('student_habits_performance.csv')

# Display the first few rows to understand the data structure
print("Data Preview:")
print(data.head())

# Exclude non-numeric columns (e.g., student_id)
numeric_data = data.select_dtypes(include=['int64', 'float64'])

# Calculate correlation matrix
correlation_matrix = numeric_data.corr()
print("\nCorrelation Matrix:")
print(correlation_matrix)

# Plot the correlation heatmap
plt.figure(figsize=(10, 8))
sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', fmt=".2f")
plt.title('Correlation Heatmap of Student Habits and Performance')
plt.savefig('correlation_heatmap.png')
print("\nCorrelation heatmap saved as 'correlation_heatmap.png'.")