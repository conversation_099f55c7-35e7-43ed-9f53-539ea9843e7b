import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimSun']
plt.rcParams['axes.unicode_minus'] = False

# 读取数据
data = pd.read_csv('student_habits_performance.csv')

# 检查数据
print("数据预览：")
print(data.head())
print("\n数据描述：")
print(data.describe())

# 绘制成绩分布直方图
plt.figure(figsize=(10, 6))
sns.histplot(data['exam_score'], bins=20, kde=True, color='skyblue')
plt.title('学生成绩分布直方图')
plt.xlabel('成绩')
plt.ylabel('频数')
plt.grid(True)
plt.savefig('student_performance_distribution.png')
plt.show()

print("成绩分布直方图已保存为 student_performance_distribution.png")