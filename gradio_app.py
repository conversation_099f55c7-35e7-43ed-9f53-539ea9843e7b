# 文件路径: gradio_app.py
# 混合型智能代理的Gradio前端界面

import gradio as gr
import os
import json
import threading
import time
import shutil
from datetime import datetime
from typing import List, Dict, Any, Optional
import pandas as pd
import matplotlib.pyplot as plt
import base64
from io import BytesIO

# 导入项目核心模块
from agent.graph import build_graph
from agent.state import State

class HybridAgentInterface:
    """混合型智能代理的Gradio界面类"""
    
    def __init__(self):
        self.work_dir = "work"
        self.graph = build_graph()
        self.current_task = None
        self.task_history = []
        self.is_running = False
        
        # 确保工作目录存在
        if not os.path.exists(self.work_dir):
            os.makedirs(self.work_dir)
    
    def get_predefined_tasks(self) -> List[str]:
        """获取预定义的任务模板"""
        return [
            "数据分析任务：对student_habits_performance.csv进行深度分析，生成包含可视化图表的报告",
            "混合研究任务：结合本地数据分析和网络研究，分析青少年睡眠与学业表现的关系",
            "自定义任务：请在下方输入框中描述您的具体需求",
            "网络研究任务：搜索并分析最新的教育科技趋势报告",
            "数据可视化任务：为现有数据创建多种类型的可视化图表"
        ]
    
    def format_complex_query(self, task_type: str, custom_input: str = "") -> str:
        """根据任务类型格式化查询"""
        if "数据分析任务" in task_type:
            return """
作为一个数据分析专家，请对工作目录下的student_habits_performance.csv文件进行全面分析：

1. **数据探索**：
   - 加载并检查数据结构
   - 生成描述性统计信息
   - 识别缺失值和异常值

2. **关系分析**：
   - 分析睡眠时长、社交媒体使用、学习时间与考试成绩的关系
   - 计算相关系数并生成相关性热力图

3. **可视化**：
   - 创建至少3个不同类型的图表（散点图、箱线图、直方图等）
   - 所有图表必须包含中文标签和说明

4. **报告生成**：
   - 生成一份详细的分析报告（Markdown格式）
   - 报告中必须包含所有生成的图表
"""
        
        elif "混合研究任务" in task_type:
            return """
作为一个混合分析机器人，你的核心任务是深度结合**本地数据分析**与**最新的网络研究**，完成一份高质量的综合性分析报告。

**你必须严格按照以下步骤执行，缺一不可：**

1. **本地数据分析**: 
   * 对工作目录下的 `student_habits_performance.csv` 文件进行深入的探索性数据分析(EDA)。
   * 分析关键变量（如睡眠时长、社交媒体使用时长）与考试成绩的关系。
   * 生成至少一个能体现核心发现的可视化图表，并保存为图片文件。

2. **网络深度研究 (强制执行步骤)**:
   * **必须使用 `internet_search` 工具**，查找并整合至少两篇关于"青少年睡眠模式、数字媒体使用与学业成绩关联的最新（2024-2025年）权威研究或报告"。
   * **必须使用 `find_images` 工具**，查找一张能够宏观展示"数字分心"（Digital Distraction）或"健康作息"概念的示意图。

3. **综合报告撰写 (必须结合网络研究成果)**:
   * 生成一份名为 `final_hybrid_report.md` 的最终报告。
   * 报告中，**必须将你在第1步的本地数据分析发现，与第2步的网络研究发现进行对比、印证或补充**。
   * 报告中**必须同时插入**你在本地生成的分析图表和你从网上找到的概念示意图，并对所有图表进行解释。
"""
        
        elif "网络研究任务" in task_type:
            return """
请进行深度的网络研究任务：

1. **信息搜索**：
   - 使用internet_search工具搜索最新的教育科技趋势
   - 查找2024-2025年的相关研究报告和数据

2. **图片收集**：
   - 使用find_images工具查找相关的概念图和示意图
   - 收集能够说明教育科技发展趋势的可视化材料

3. **报告整理**：
   - 将搜索到的信息整理成结构化的报告
   - 包含引用来源和图片说明
"""
        
        elif "自定义任务" in task_type and custom_input:
            return custom_input
        
        else:
            return """
请创建数据可视化图表：

1. **数据加载**：加载student_habits_performance.csv文件
2. **图表创建**：生成多种类型的可视化图表
3. **图表保存**：将所有图表保存为PNG格式
4. **说明文档**：为每个图表创建说明文档
"""
    
    def run_agent_task(self, query: str, progress_callback=None) -> Dict[str, Any]:
        """运行智能代理任务"""
        self.is_running = True
        self.current_task = {
            "query": query,
            "start_time": datetime.now(),
            "status": "running",
            "steps": [],
            "final_report": "",
            "generated_files": []
        }
        
        try:
            # 准备输入
            inputs = {"user_message": query}
            config = {"recursion_limit": 150}
            
            # 流式执行图
            step_count = 0
            for event in self.graph.stream(inputs, config=config):
                if not self.is_running:  # 检查是否被停止
                    break
                    
                for key, value in event.items():
                    if not value or not isinstance(value, dict):
                        continue
                    
                    step_count += 1
                    step_info = {
                        "step": step_count,
                        "node": key,
                        "timestamp": datetime.now().strftime("%H:%M:%S"),
                        "content": str(value)
                    }
                    
                    self.current_task["steps"].append(step_info)
                    
                    # 更新进度
                    if progress_callback:
                        progress_callback(f"执行步骤 {step_count}: {key}", step_count * 10)
                    
                    # 捕获最终报告
                    if key == "report" and 'final_report' in value:
                        self.current_task["final_report"] = value['final_report']
                        
                        # 保存报告到文件
                        report_path = os.path.join(self.work_dir, "final_analysis_report.md")
                        try:
                            with open(report_path, "w", encoding='utf-8') as f:
                                f.write(value['final_report'])
                            self.current_task["generated_files"].append(report_path)
                        except Exception as e:
                            print(f"保存报告时出错: {e}")
            
            # 扫描生成的文件
            self.scan_generated_files()
            
            self.current_task["status"] = "completed"
            self.current_task["end_time"] = datetime.now()
            
            # 添加到历史记录
            self.task_history.append(self.current_task.copy())
            
            return self.current_task
            
        except Exception as e:
            self.current_task["status"] = "error"
            self.current_task["error"] = str(e)
            self.current_task["end_time"] = datetime.now()
            return self.current_task
        finally:
            self.is_running = False
    
    def scan_generated_files(self):
        """扫描工作目录中生成的文件"""
        if not os.path.exists(self.work_dir):
            return
        
        generated_files = []
        for root, dirs, files in os.walk(self.work_dir):
            for file in files:
                file_path = os.path.join(root, file)
                # 获取文件的修改时间
                mtime = os.path.getmtime(file_path)
                file_info = {
                    "path": file_path,
                    "name": file,
                    "size": os.path.getsize(file_path),
                    "modified": datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M:%S"),
                    "type": self.get_file_type(file)
                }
                generated_files.append(file_info)
        
        # 按修改时间排序
        generated_files.sort(key=lambda x: x["modified"], reverse=True)
        self.current_task["generated_files"] = generated_files
    
    def get_file_type(self, filename: str) -> str:
        """根据文件扩展名确定文件类型"""
        ext = os.path.splitext(filename)[1].lower()
        if ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp']:
            return 'image'
        elif ext in ['.md', '.txt']:
            return 'text'
        elif ext in ['.csv', '.xlsx', '.xls']:
            return 'data'
        elif ext in ['.py']:
            return 'code'
        else:
            return 'other'
    
    def stop_current_task(self):
        """停止当前运行的任务"""
        self.is_running = False
        if self.current_task:
            self.current_task["status"] = "stopped"
            self.current_task["end_time"] = datetime.now()
    
    def get_file_content(self, file_path: str) -> str:
        """获取文件内容"""
        try:
            if not os.path.exists(file_path):
                return "文件不存在"
            
            file_type = self.get_file_type(file_path)
            
            if file_type == 'image':
                return f"图片文件: {file_path}"
            elif file_type in ['text', 'code']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            elif file_type == 'data':
                if file_path.endswith('.csv'):
                    df = pd.read_csv(file_path)
                    return f"CSV文件预览 (前10行):\n{df.head(10).to_string()}"
                else:
                    return "数据文件，请下载查看"
            else:
                return "无法预览此文件类型"
        except Exception as e:
            return f"读取文件时出错: {str(e)}"

# 创建全局实例
agent_interface = HybridAgentInterface()

def create_gradio_interface():
    """创建Gradio界面"""

    # 自定义CSS样式
    custom_css = """
    .task-container {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        background-color: #f9f9f9;
    }
    .status-running {
        color: #ff6b35;
        font-weight: bold;
    }
    .status-completed {
        color: #28a745;
        font-weight: bold;
    }
    .status-error {
        color: #dc3545;
        font-weight: bold;
    }
    .file-item {
        border: 1px solid #eee;
        border-radius: 4px;
        padding: 8px;
        margin: 4px 0;
        background-color: white;
    }
    """

    def update_task_selection(task_type):
        """根据任务类型更新界面"""
        if "自定义任务" in task_type:
            return gr.update(visible=True, placeholder="请详细描述您的任务需求...")
        else:
            return gr.update(visible=False)

    def start_task(task_type, custom_input, progress=gr.Progress()):
        """启动任务"""
        if agent_interface.is_running:
            return "❌ 当前有任务正在运行，请等待完成或停止当前任务", "", ""

        # 格式化查询
        query = agent_interface.format_complex_query(task_type, custom_input)

        # 在新线程中运行任务
        def run_task():
            def progress_callback(message, step):
                progress(step/100, desc=message)

            result = agent_interface.run_agent_task(query, progress_callback)
            return result

        # 启动任务
        progress(0, desc="正在启动任务...")
        result = run_task()

        if result["status"] == "completed":
            status_msg = f"✅ 任务完成！耗时: {(result['end_time'] - result['start_time']).total_seconds():.1f}秒"
            report_content = result.get("final_report", "未生成报告")
            files_info = format_files_info(result.get("generated_files", []))
        elif result["status"] == "error":
            status_msg = f"❌ 任务执行出错: {result.get('error', '未知错误')}"
            report_content = ""
            files_info = ""
        else:
            status_msg = f"⏸️ 任务已停止"
            report_content = result.get("final_report", "")
            files_info = format_files_info(result.get("generated_files", []))

        return status_msg, report_content, files_info

    def stop_task():
        """停止当前任务"""
        agent_interface.stop_current_task()
        return "⏸️ 任务已停止"

    def format_files_info(files):
        """格式化文件信息"""
        if not files:
            return "暂无生成文件"

        info_lines = []
        for file_info in files:
            file_type_emoji = {
                'image': '🖼️',
                'text': '📄',
                'data': '📊',
                'code': '💻',
                'other': '📁'
            }
            emoji = file_type_emoji.get(file_info['type'], '📁')
            size_kb = file_info['size'] / 1024
            info_lines.append(
                f"{emoji} **{file_info['name']}** ({size_kb:.1f}KB) - {file_info['modified']}"
            )

        return "\n".join(info_lines)

    def refresh_files():
        """刷新文件列表"""
        if agent_interface.current_task:
            agent_interface.scan_generated_files()
            files_info = format_files_info(agent_interface.current_task.get("generated_files", []))
            return files_info
        return "暂无任务运行"

    def get_file_list():
        """获取文件列表用于下拉选择"""
        if not os.path.exists(agent_interface.work_dir):
            return []

        files = []
        for root, dirs, filenames in os.walk(agent_interface.work_dir):
            for filename in filenames:
                files.append(os.path.join(root, filename))
        return files

    def preview_file(file_path):
        """预览文件内容"""
        if not file_path:
            return "请选择要预览的文件"

        content = agent_interface.get_file_content(file_path)

        # 如果是图片文件，返回图片路径用于显示
        if agent_interface.get_file_type(file_path) == 'image':
            return content, file_path
        else:
            return content, None

    def get_task_history():
        """获取任务历史"""
        if not agent_interface.task_history:
            return "暂无历史任务"

        history_lines = []
        for i, task in enumerate(agent_interface.task_history[-5:], 1):  # 显示最近5个任务
            status_emoji = {
                'completed': '✅',
                'error': '❌',
                'stopped': '⏸️'
            }
            emoji = status_emoji.get(task['status'], '❓')
            start_time = task['start_time'].strftime("%m-%d %H:%M")
            duration = (task.get('end_time', task['start_time']) - task['start_time']).total_seconds()

            history_lines.append(
                f"{emoji} **任务 {i}** ({start_time}) - {duration:.1f}秒\n"
                f"   查询: {task['query'][:100]}{'...' if len(task['query']) > 100 else ''}"
            )

        return "\n\n".join(history_lines)

    # 创建Gradio界面
    with gr.Blocks(css=custom_css, title="混合型智能代理", theme=gr.themes.Soft()) as demo:
        gr.Markdown("""
        # 🤖 混合型智能代理 (Hybrid Agent)

        这是一个强大的端到端智能代理，结合了**本地数据分析**和**互联网研究**能力。
        您可以通过简单的自然语言描述，让代理自动完成复杂的数据分析和研究任务。
        """)

        with gr.Row():
            with gr.Column(scale=2):
                gr.Markdown("### 📋 任务配置")

                task_type = gr.Dropdown(
                    choices=agent_interface.get_predefined_tasks(),
                    label="选择任务类型",
                    value=agent_interface.get_predefined_tasks()[0],
                    interactive=True
                )

                custom_input = gr.Textbox(
                    label="自定义任务描述",
                    placeholder="请详细描述您的任务需求...",
                    lines=4,
                    visible=False
                )

                with gr.Row():
                    start_btn = gr.Button("🚀 开始执行", variant="primary", size="lg")
                    stop_btn = gr.Button("⏹️ 停止任务", variant="secondary")

                task_status = gr.Textbox(
                    label="任务状态",
                    value="准备就绪",
                    interactive=False,
                    lines=2
                )

            with gr.Column(scale=1):
                gr.Markdown("### 📊 系统状态")

                system_info = gr.Markdown(f"""
                **工作目录**: `{agent_interface.work_dir}`
                **当前状态**: 空闲
                **后端服务**: 需要启动SearxNG (端口8088)
                """)

                refresh_btn = gr.Button("🔄 刷新状态", size="sm")

        gr.Markdown("---")

        with gr.Row():
            with gr.Column():
                gr.Markdown("### 📄 执行结果")

                report_output = gr.Textbox(
                    label="生成的报告",
                    lines=15,
                    max_lines=20,
                    interactive=False,
                    placeholder="任务完成后，生成的报告将在这里显示..."
                )

            with gr.Column():
                gr.Markdown("### 📁 生成文件")

                files_info = gr.Markdown("暂无生成文件")

                file_selector = gr.Dropdown(
                    label="选择文件预览",
                    choices=[],
                    interactive=True
                )

                preview_btn = gr.Button("👁️ 预览文件", size="sm")

                file_preview = gr.Textbox(
                    label="文件内容预览",
                    lines=10,
                    interactive=False
                )

                image_preview = gr.Image(
                    label="图片预览",
                    visible=False
                )

        gr.Markdown("---")

        with gr.Row():
            with gr.Column():
                gr.Markdown("### 📈 任务历史")
                history_display = gr.Markdown("暂无历史任务")
                history_refresh_btn = gr.Button("🔄 刷新历史", size="sm")

        # 事件绑定
        task_type.change(
            fn=update_task_selection,
            inputs=[task_type],
            outputs=[custom_input]
        )

        start_btn.click(
            fn=start_task,
            inputs=[task_type, custom_input],
            outputs=[task_status, report_output, files_info]
        )

        stop_btn.click(
            fn=stop_task,
            outputs=[task_status]
        )

        refresh_btn.click(
            fn=refresh_files,
            outputs=[files_info]
        )

        refresh_btn.click(
            fn=get_file_list,
            outputs=[file_selector]
        )

        preview_btn.click(
            fn=preview_file,
            inputs=[file_selector],
            outputs=[file_preview, image_preview]
        )

        history_refresh_btn.click(
            fn=get_task_history,
            outputs=[history_display]
        )

        # 定期刷新文件列表
        demo.load(
            fn=get_file_list,
            outputs=[file_selector]
        )

    return demo

if __name__ == "__main__":
    # 创建并启动Gradio界面
    demo = create_gradio_interface()

    print("🚀 正在启动混合型智能代理界面...")
    print("📝 请确保已经启动SearxNG后端服务 (端口8088)")
    print("🔧 请确保已经配置好API密钥")

    demo.launch(
        server_name="0.0.0.0",  # 允许外部访问
        server_port=7860,       # 默认端口
        share=False,            # 不创建公共链接
        debug=True,             # 启用调试模式
        show_error=True         # 显示错误信息
    )
