# 混合型智能代理 - Gradio Web界面使用指南

## 🎯 概述

本项目为混合型智能代理提供了一个功能完整的Web界面，基于Gradio框架构建。用户可以通过直观的Web界面与智能代理交互，无需编写代码即可完成复杂的数据分析和研究任务。

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <项目地址>
cd mcp_agent

# 安装依赖
pip install -r requirements.txt

# 或使用自动安装脚本
python setup.py
```

### 2. 配置环境

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件，填入API密钥
nano .env
```

### 3. 启动服务

```bash
# 启动SearxNG后端 (可选，用于网络搜索)
cd backend/searxng-docker-master
docker compose up -d
cd ../..

# 启动Web界面
python start_gradio.py

# 或直接运行
python gradio_app.py
```

### 4. 访问界面

打开浏览器访问: `http://localhost:7860`

## 🎨 界面功能

### 主要区域

1. **任务配置区** - 选择或自定义任务
2. **系统状态区** - 显示服务状态和配置信息
3. **执行结果区** - 显示生成的报告内容
4. **文件管理区** - 管理和预览生成的文件
5. **任务历史区** - 查看历史任务记录

### 核心功能

#### 📋 任务管理
- **预定义任务**: 提供5种常用任务模板
- **自定义任务**: 支持自然语言描述的复杂需求
- **实时进度**: 显示任务执行的实时状态
- **任务控制**: 支持启动、停止任务

#### 📊 结果展示
- **报告预览**: 在线查看Markdown格式的分析报告
- **实时更新**: 任务执行过程中实时更新结果
- **格式化显示**: 支持富文本格式的报告展示

#### 📁 文件管理
- **文件列表**: 显示所有生成的文件及其信息
- **文件预览**: 支持文本、图片、数据文件的在线预览
- **文件下载**: 方便下载生成的文件到本地
- **文件分类**: 按类型分类显示文件

#### 🔧 系统监控
- **服务检查**: 自动检查后端服务状态
- **依赖检查**: 验证必要的Python包是否已安装
- **配置验证**: 检查API密钥等配置是否正确

## 📝 使用示例

### 示例1: 数据分析任务

1. 选择"数据分析任务"
2. 点击"开始执行"
3. 系统自动:
   - 加载CSV数据文件
   - 进行探索性数据分析
   - 生成统计图表
   - 创建分析报告
4. 在界面中查看结果和下载文件

### 示例2: 混合研究任务

1. 选择"混合研究任务"
2. 系统执行:
   - 本地数据分析
   - 网络信息搜索
   - 整合分析结果
   - 生成综合报告
3. 查看包含本地分析和网络研究的完整报告

### 示例3: 自定义任务

1. 选择"自定义任务"
2. 在文本框中输入具体需求，例如:
   ```
   请分析学生数据中的性别差异，
   生成按性别分组的成绩分布图，
   并提供统计学显著性检验结果
   ```
3. 系统根据描述自动规划和执行任务

## 🛠️ 高级配置

### 环境变量配置

```bash
# API配置
DEEPSEEK_API_KEY=your_api_key
OPENAI_API_KEY=your_api_key

# 服务配置
GRADIO_SERVER_PORT=7860
SEARXNG_URL=http://localhost:8088

# 任务配置
TASK_TIMEOUT=3600
MAX_FILE_SIZE=100
```

### 自定义任务模板

可以在`gradio_app.py`中的`get_predefined_tasks()`方法中添加新的任务模板:

```python
def get_predefined_tasks(self) -> List[str]:
    return [
        "数据分析任务：...",
        "混合研究任务：...",
        "您的自定义任务模板：...",  # 添加新模板
        "自定义任务：..."
    ]
```

## 🔍 故障排除

### 常见问题

1. **界面无法启动**
   - 检查Python版本 (需要3.8+)
   - 确认已安装所有依赖
   - 查看错误日志

2. **任务执行失败**
   - 检查API密钥配置
   - 确认网络连接正常
   - 查看任务日志

3. **网络搜索不工作**
   - 确认SearxNG服务已启动
   - 检查Docker是否运行
   - 验证端口8088是否可访问

4. **文件预览问题**
   - 检查文件权限
   - 确认文件格式支持
   - 查看浏览器控制台错误

### 调试模式

启用调试模式获取更多信息:

```bash
# 启用调试模式
python gradio_app.py --debug

# 查看详细日志
tail -f agent.log
```

## 📚 技术架构

### 核心组件

- **gradio_app.py**: 主界面应用
- **file_manager.py**: 文件管理组件
- **start_gradio.py**: 启动脚本
- **demo_gradio.py**: 演示版本

### 数据流

```
用户输入 → 任务解析 → 代理执行 → 结果生成 → 界面展示
    ↓           ↓           ↓           ↓           ↓
  Web界面 → 任务队列 → LangGraph → 文件系统 → 实时更新
```

### 文件结构

```
mcp_agent/
├── gradio_app.py          # 主界面应用
├── start_gradio.py        # 启动脚本
├── demo_gradio.py         # 演示版本
├── file_manager.py        # 文件管理
├── requirements.txt       # 依赖列表
├── .env.example          # 配置模板
├── work/                 # 工作目录
│   ├── *.csv            # 数据文件
│   ├── *.png            # 生成图表
│   ├── *.md             # 分析报告
│   └── *.py             # 分析脚本
└── agent/               # 代理核心
    ├── graph.py         # 工作流图
    ├── nodes.py         # 节点实现
    ├── tools.py         # 工具集
    └── state.py         # 状态管理
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目:

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 📞 支持

如有问题或建议，请:
- 提交GitHub Issue
- 查看项目文档
- 联系项目维护者
