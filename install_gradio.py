#!/usr/bin/env python3
# 文件路径: install_gradio.py
# 安装Gradio和相关依赖的脚本

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        return False

def main():
    """主安装流程"""
    print("🚀 安装Gradio界面依赖包")
    print("=" * 40)
    
    # 必需的包列表
    required_packages = [
        "gradio>=4.0.0",
        "pandas",
        "matplotlib", 
        "seaborn",
        "Pillow",
        "python-dotenv"
    ]
    
    # 可选的包列表（用于完整功能）
    optional_packages = [
        "langchain>=0.2.0",
        "langgraph>=0.1.0", 
        "langchain-openai",
        "trio>=0.25.1",
        "mcp>=0.2.0"
    ]
    
    print("安装必需依赖...")
    success_count = 0
    for package in required_packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n必需依赖安装完成: {success_count}/{len(required_packages)}")
    
    if success_count == len(required_packages):
        print("\n✅ 基础依赖安装成功！可以运行演示界面")
        print("运行命令: python demo_gradio.py")
        
        # 询问是否安装完整功能依赖
        try:
            install_full = input("\n是否安装完整功能依赖? (y/n): ").lower().strip()
            if install_full in ['y', 'yes']:
                print("\n安装完整功能依赖...")
                for package in optional_packages:
                    install_package(package)
                print("\n✅ 完整功能依赖安装完成！")
                print("运行命令: python start_gradio.py")
        except KeyboardInterrupt:
            print("\n安装中断")
    else:
        print("\n❌ 部分依赖安装失败，请检查网络连接和Python环境")

if __name__ == "__main__":
    main()
