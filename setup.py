#!/usr/bin/env python3
# 文件路径: setup.py
# 混合型智能代理项目安装脚本

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, description=""):
    """运行命令并处理错误"""
    print(f"🔧 {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - 完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - 失败")
        print(f"错误信息: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要Python 3.8或更高版本")
        return False
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_dependencies():
    """安装Python依赖"""
    print("📦 安装Python依赖包...")
    
    # 升级pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "升级pip"):
        return False
    
    # 安装核心依赖
    core_packages = [
        "langchain>=0.2.0",
        "langgraph>=0.1.0", 
        "langchain-openai",
        "trio>=0.25.1",
        "mcp>=0.2.0",
        "pandas",
        "gradio",
        "matplotlib",
        "seaborn",
        "python-dotenv",
        "requests",
        "Pillow"
    ]
    
    for package in core_packages:
        if not run_command(f"{sys.executable} -m pip install {package}", f"安装 {package}"):
            print(f"⚠️ 安装 {package} 失败，但继续安装其他包")
    
    # 尝试从requirements.txt安装
    if Path("requirements.txt").exists():
        run_command(f"{sys.executable} -m pip install -r requirements.txt", "从requirements.txt安装依赖")
    
    return True

def setup_environment():
    """设置环境"""
    print("🔧 设置项目环境...")
    
    # 创建工作目录
    work_dir = Path("work")
    work_dir.mkdir(exist_ok=True)
    print("✅ 创建工作目录: work/")
    
    # 复制环境变量配置文件
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if env_example.exists() and not env_file.exists():
        shutil.copy(env_example, env_file)
        print("✅ 创建环境配置文件: .env")
        print("⚠️ 请编辑 .env 文件，填入您的API密钥")
    
    # 检查示例数据文件
    csv_files = list(Path(".").glob("*.csv"))
    if csv_files:
        for csv_file in csv_files:
            target = work_dir / csv_file.name
            if not target.exists():
                shutil.copy(csv_file, target)
                print(f"✅ 复制数据文件: {csv_file.name} -> work/")
    
    return True

def check_docker():
    """检查Docker是否可用"""
    print("🐳 检查Docker环境...")
    
    try:
        result = subprocess.run("docker --version", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Docker已安装")
            
            # 检查Docker是否运行
            result = subprocess.run("docker info", shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Docker服务正在运行")
                return True
            else:
                print("⚠️ Docker已安装但未运行，请启动Docker服务")
                return False
        else:
            print("⚠️ Docker未安装")
            return False
    except:
        print("⚠️ 无法检查Docker状态")
        return False

def setup_backend():
    """设置后端服务"""
    print("🔧 设置SearxNG后端服务...")
    
    backend_dir = Path("backend/searxng-docker-master")
    if backend_dir.exists():
        print("✅ 找到SearxNG后端目录")
        
        if check_docker():
            print("💡 您可以运行以下命令启动后端服务:")
            print(f"   cd {backend_dir}")
            print("   docker compose up -d")
        else:
            print("⚠️ 需要安装并启动Docker才能运行后端服务")
    else:
        print("⚠️ 未找到SearxNG后端目录")
        print("请确保 backend/searxng-docker-master 目录存在")
    
    return True

def main():
    """主安装流程"""
    print("🚀 混合型智能代理 - 项目安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    print()
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败")
        sys.exit(1)
    
    print()
    
    # 设置环境
    if not setup_environment():
        print("❌ 环境设置失败")
        sys.exit(1)
    
    print()
    
    # 检查后端
    setup_backend()
    
    print()
    print("=" * 50)
    print("🎉 安装完成!")
    print()
    print("📝 下一步操作:")
    print("1. 编辑 .env 文件，填入您的API密钥")
    print("2. 启动SearxNG后端服务 (如果需要网络搜索功能)")
    print("3. 运行 python start_gradio.py 启动Web界面")
    print()
    print("💡 快速启动命令:")
    print("   python start_gradio.py")
    print()
    print("🌐 Web界面地址: http://localhost:7860")

if __name__ == "__main__":
    main()
