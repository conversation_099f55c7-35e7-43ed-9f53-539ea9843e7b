# 文件路径: hybrid_agent/README.md

# 混合型智能代理 (Hybrid Agent)

这是一个强大的、端到端的智能代理项目，它结合了结构化的工作流（使用 LangGraph）和动态的互联网研究能力（使用 MCP 和 SearxNG）。

## 项目特性

- **能力互补**：既能执行本地Python脚本进行数据分析，也能连接互联网获取最新信息。
- **结构化与灵活性统一**：通过预设的图结构保证任务按计划执行，同时允许在研究阶段灵活探索。
- **端到端自动化**：从一个复杂需求输入，到最终图文并茂的报告输出，全程自动化。

## 安装与设置

1.  **克隆或下载项目**：
    将 `hybrid_agent` 文件夹放置在您的工作区。

2.  **安装Python依赖**：
    ```bash
    pip install "langchain>=0.2.0" "langgraph>=0.1.0" langchain-openai "trio>=0.25.1" "mcp.py>=0.2.0" pandas gradio matplotlib seaborn
    ```

3.  **设置后端服务**：
    - 进入 `backend/searxng-docker-master` 目录。
    - 根据其 `README.md` 的指示，使用 Docker Compose 启动 SearxNG 服务。确保它在 `8088` 端口运行。
    - **注意**: 后端服务（Docker容器）必须在运行主代理程序之前启动并保持运行。

4.  **配置API密钥**：
    - 打开 `agent/nodes.py` 文件。
    - 将 `llm = ChatOpenAI(...)` 中的 `YOUR_BASE_URL` 和 `YOUR_API_KEY` 替换为您自己的有效凭据。

## 如何运行

### 方式一：使用Gradio Web界面 (推荐)

1.  **启动后端服务**：
    - 确保 Docker 正在运行。
    - 在终端中，导航到 `hybrid_agent/backend/searxng-docker-master` 目录。
    - 运行 `docker compose up -d`。

2.  **启动Web界面**：
    - 在项目根目录运行：
      ```bash
      python start_gradio.py
      ```
    - 或者直接运行：
      ```bash
      python gradio_app.py
      ```

3.  **使用Web界面**：
    - 打开浏览器访问 `http://localhost:7860`
    - 选择预定义任务或输入自定义任务描述
    - 点击"开始执行"启动任务
    - 实时查看执行进度和结果
    - 在界面中预览和下载生成的文件

### 方式二：命令行运行

1.  **启动后端服务**：
    - 确保 Docker 正在运行。
    - 在终端中，导航到 `hybrid_agent/backend/searxng-docker-master` 目录。
    - 运行 `docker compose up -d`。

2.  **运行主代理**：
    - 在新的终端中，导航到 `hybrid_agent` 项目的根目录。
    - 运行主程序：
      ```bash
      python run.py
      ```

3.  **查看结果**：
    - 代理的执行过程将会实时打印在控制台。
    - 任务完成后，最终的报告 `final_analysis_report.md` 以及过程中产生的任何图表或脚本都将保存在 `work/` 目录下。

## Web界面功能特性

### 🎯 任务管理
- **预定义任务模板**：提供常用的数据分析和研究任务模板
- **自定义任务**：支持自然语言描述的复杂任务需求
- **实时进度显示**：显示任务执行的实时状态和进度
- **任务历史**：记录和查看历史任务执行情况

### 📊 结果展示
- **报告预览**：在线预览生成的Markdown报告
- **文件管理**：列出所有生成的文件（图表、数据、脚本等）
- **文件预览**：支持文本、图片、数据文件的在线预览
- **文件下载**：方便下载生成的所有文件

### 🔧 系统监控
- **服务状态检查**：检查后端服务和API配置状态
- **依赖检查**：自动检查必要的Python包是否已安装
- **错误处理**：友好的错误信息显示和处理

## 代理工作流程

1.  **规划 (Plan)**: `run.py` 发出复杂请求，`create_planner_node` 将其分解为包含本地分析和网络搜索的多个步骤。
2.  **执行 (Execute)**: `execute_node` 逐一执行计划。
    - 当遇到数据分析步骤时，它会调用 `shell_exec` 工具来运行Python脚本。
    - 当遇到研究步骤时，它会调用 `internet_search` 工具，该工具通过 `mcp_client` 与后端 `search_mcp.py` 通信。
3.  **更新 (Update)**: `update_planner_node` 在每一步后更新计划状态，并决定是继续执行还是生成报告。
4.  **报告 (Report)**: `report_node` 收集所有 `observations`（包括本地分析的输出和网络研究的结果），生成最终的综合报告。

## 使用示例

### 通过Web界面使用

1. 启动界面后，选择"数据分析任务"
2. 系统会自动分析 `student_habits_performance.csv` 文件
3. 生成包含统计分析、相关性分析和可视化图表的完整报告
4. 在界面中直接预览生成的图表和报告
5. 下载所需的文件到本地

### 预定义任务类型

- **数据分析任务**：深度分析CSV数据，生成统计报告和可视化图表
- **混合研究任务**：结合本地数据分析和最新网络研究
- **网络研究任务**：搜索和整理最新的行业报告和趋势
- **数据可视化任务**：专注于创建各种类型的数据图表
- **自定义任务**：根据用户具体需求执行定制化分析

## 🎨 Gradio Web界面

### 快速体验

如果您想快速体验界面功能，可以运行演示版本：

```bash
# 安装基础依赖
python install_gradio.py

# 运行演示界面
python demo_gradio.py
```

演示界面提供模拟的执行结果，让您了解完整版本的功能。

### 完整功能

要使用完整的AI分析功能：

```bash
# 1. 安装所有依赖
pip install -r requirements.txt

# 2. 配置API密钥
cp .env.example .env
# 编辑 .env 文件，填入您的API密钥

# 3. 启动后端服务 (可选)
cd backend/searxng-docker-master
docker compose up -d
cd ../..

# 4. 启动完整界面
python start_gradio.py
```

### 界面特性

- 🎯 **直观操作**: 无需编程，通过Web界面完成复杂分析
- 📊 **实时预览**: 实时查看任务执行进度和结果
- 📁 **文件管理**: 在线预览和下载生成的图表、报告
- 🔧 **状态监控**: 自动检查服务状态和配置
- 📱 **响应式设计**: 支持桌面和移动设备访问

## 📁 项目文件结构

```
mcp_agent/
├── 🎨 Web界面
│   ├── gradio_app.py          # 主界面应用
│   ├── start_gradio.py        # 启动脚本
│   ├── demo_gradio.py         # 演示版本
│   ├── file_manager.py        # 文件管理组件
│   └── install_gradio.py      # 依赖安装脚本
├── 🤖 智能代理核心
│   ├── agent/
│   │   ├── graph.py           # LangGraph工作流
│   │   ├── nodes.py           # 节点实现
│   │   ├── tools.py           # 工具集合
│   │   ├── state.py           # 状态管理
│   │   ├── prompts.py         # 提示模板
│   │   └── mcp_client.py      # MCP客户端
│   └── run.py                 # 命令行入口
├── 🔧 配置和依赖
│   ├── requirements.txt       # Python依赖
│   ├── .env.example          # 环境配置模板
│   ├── setup.py              # 自动安装脚本
│   └── GRADIO_GUIDE.md       # 详细使用指南
├── 🌐 后端服务
│   └── backend/
│       ├── search_mcp.py      # MCP搜索服务
│       └── searxng-docker-master/  # SearxNG搜索引擎
└── 📊 工作目录
    └── work/                  # 生成的文件和报告
```

## 🚀 快速开始指南

### 1. 最简单的体验方式

```bash
# 下载项目
git clone <项目地址>
cd mcp_agent

# 体验演示界面
python install_gradio.py
python demo_gradio.py
```

### 2. 完整功能部署

```bash
# 自动安装和配置
python setup.py

# 手动配置API密钥
nano .env

# 启动完整服务
python start_gradio.py
```

### 3. 访问界面

打开浏览器访问: `http://localhost:7860`