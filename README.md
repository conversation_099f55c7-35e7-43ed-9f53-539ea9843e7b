# 文件路径: hybrid_agent/README.md

# 混合型智能代理 (Hybrid Agent)

这是一个强大的、端到端的智能代理项目，它结合了结构化的工作流（使用 LangGraph）和动态的互联网研究能力（使用 MCP 和 SearxNG）。

## 项目特性

- **能力互补**：既能执行本地Python脚本进行数据分析，也能连接互联网获取最新信息。
- **结构化与灵活性统一**：通过预设的图结构保证任务按计划执行，同时允许在研究阶段灵活探索。
- **端到端自动化**：从一个复杂需求输入，到最终图文并茂的报告输出，全程自动化。

## 安装与设置

1.  **克隆或下载项目**：
    将 `hybrid_agent` 文件夹放置在您的工作区。

2.  **安装Python依赖**：
    ```bash
    pip install "langchain>=0.2.0" "langgraph>=0.1.0" langchain-openai "trio>=0.25.1" "mcp.py>=0.2.0" pandas
    ```

3.  **设置后端服务**：
    - 进入 `backend/searxng-docker-master` 目录。
    - 根据其 `README.md` 的指示，使用 Docker Compose 启动 SearxNG 服务。确保它在 `8088` 端口运行。
    - **注意**: 后端服务（Docker容器）必须在运行主代理程序之前启动并保持运行。

4.  **配置API密钥**：
    - 打开 `agent/nodes.py` 文件。
    - 将 `llm = ChatOpenAI(...)` 中的 `YOUR_BASE_URL` 和 `YOUR_API_KEY` 替换为您自己的有效凭据。

## 如何运行

1.  **启动后端服务**：
    - 确保 Docker 正在运行。
    - 在终端中，导航到 `hybrid_agent/backend/searxng-docker-master` 目录。
    - 运行 `docker compose up -d`。

2.  **运行主代理**：
    - 在新的终端中，导航到 `hybrid_agent` 项目的根目录。
    - 运行主程序：
      ```bash
      python run.py
      ```

3.  **查看结果**：
    - 代理的执行过程将会实时打印在控制台。
    - 任务完成后，最终的报告 `final_analysis_report.md` 以及过程中产生的任何图表或脚本都将保存在 `work/` 目录下。

## 代理工作流程

1.  **规划 (Plan)**: `run.py` 发出复杂请求，`create_planner_node` 将其分解为包含本地分析和网络搜索的多个步骤。
2.  **执行 (Execute)**: `execute_node` 逐一执行计划。
    - 当遇到数据分析步骤时，它会调用 `shell_exec` 工具来运行Python脚本。
    - 当遇到研究步骤时，它会调用 `internet_search` 工具，该工具通过 `mcp_client` 与后端 `search_mcp.py` 通信。
3.  **更新 (Update)**: `update_planner_node` 在每一步后更新计划状态，并决定是继续执行还是生成报告。
4.  **报告 (Report)**: `report_node` 收集所有 `observations`（包括本地分析的输出和网络研究的结果），生成最终的综合报告。