#!/usr/bin/env python3
# 文件路径: demo_gradio.py
# 混合型智能代理的简化演示界面

import gradio as gr
import os
import json
import time
from datetime import datetime
from pathlib import Path

def create_demo_interface():
    """创建演示界面"""
    
    # 模拟任务执行
    def run_demo_task(task_type, custom_input):
        """演示任务执行"""
        if "自定义任务" in task_type and not custom_input.strip():
            return "❌ 请输入自定义任务描述", "", "暂无生成文件"
        
        # 模拟执行过程
        time.sleep(1)
        
        status = "✅ 演示任务完成！"
        
        if "数据分析" in task_type:
            report = """# 数据分析报告 (演示)

## 数据概览
- 数据文件: student_habits_performance.csv
- 样本数量: 1000名学生
- 主要变量: 睡眠时长、社交媒体使用、学习时间、考试成绩

## 关键发现
1. **睡眠与成绩正相关**: 睡眠时间越长，考试成绩越好
2. **社交媒体负影响**: 过度使用社交媒体会影响学业表现
3. **学习时间效应**: 有效学习时间与成绩呈正相关

## 建议
- 保证充足睡眠 (7-9小时)
- 合理控制社交媒体使用时间
- 提高学习效率而非单纯增加学习时间

*注: 这是演示报告，实际使用时会生成详细的数据分析和可视化图表*
"""
            files_info = """🖼️ **correlation_heatmap.png** (45.2KB) - 相关性热力图
📄 **analysis_report.md** (12.8KB) - 详细分析报告  
💻 **data_analysis.py** (8.5KB) - 分析脚本
📊 **summary_stats.csv** (3.2KB) - 统计摘要"""
            
        elif "混合研究" in task_type:
            report = """# 混合研究报告 (演示)

## 本地数据分析
基于student_habits_performance.csv的分析显示:
- 睡眠不足(<6小时)的学生平均成绩比充足睡眠(>8小时)的学生低15分
- 每日社交媒体使用超过3小时的学生成绩普遍较低

## 网络研究发现
最新研究表明:
- 2024年《青少年数字健康报告》指出数字分心是影响学业的主要因素
- 哈佛大学研究发现规律作息能提升认知能力20%

## 综合结论
本地数据与最新研究高度一致，证实了睡眠质量和数字媒体使用对学业表现的重要影响。

*注: 这是演示报告，实际使用时会整合真实的网络搜索结果*
"""
            files_info = """🖼️ **sleep_score_analysis.png** (52.1KB) - 睡眠与成绩分析图
🖼️ **digital_distraction_concept.jpg** (128.5KB) - 数字分心概念图
📄 **hybrid_research_report.md** (25.3KB) - 综合研究报告
💻 **correlation_analysis.py** (15.2KB) - 相关性分析脚本"""
            
        else:
            report = f"""# 任务执行报告 (演示)

## 任务类型
{task_type}

## 执行内容
{'用户自定义: ' + custom_input if custom_input else '预定义任务模板'}

## 执行结果
演示任务已成功完成。在实际使用中，系统会:
1. 自动分解复杂任务为可执行步骤
2. 调用相应的工具完成数据分析或网络研究
3. 生成包含图表和详细分析的完整报告

*注: 这是演示模式，请安装完整依赖后使用完整功能*
"""
            files_info = """📄 **demo_report.md** (8.5KB) - 演示报告
💻 **demo_script.py** (3.2KB) - 演示脚本"""
        
        return status, report, files_info
    
    def update_task_input(task_type):
        """更新任务输入框"""
        if "自定义任务" in task_type:
            return gr.update(visible=True, placeholder="请详细描述您的任务需求...")
        else:
            return gr.update(visible=False)
    
    def get_demo_files():
        """获取演示文件列表"""
        work_dir = Path("work")
        if not work_dir.exists():
            return []
        
        files = []
        for file_path in work_dir.iterdir():
            if file_path.is_file():
                files.append(str(file_path))
        return files
    
    def preview_demo_file(file_path):
        """预览演示文件"""
        if not file_path:
            return "请选择要预览的文件", None
        
        path = Path(file_path)
        if not path.exists():
            return "文件不存在", None
        
        try:
            if path.suffix.lower() in ['.png', '.jpg', '.jpeg', '.gif']:
                return f"图片文件: {path.name}", str(path)
            elif path.suffix.lower() in ['.md', '.txt', '.py']:
                with open(path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return content[:2000] + ("..." if len(content) > 2000 else ""), None
            elif path.suffix.lower() == '.csv':
                return f"CSV数据文件: {path.name}\n文件大小: {path.stat().st_size} 字节", None
            else:
                return f"文件类型: {path.suffix}\n文件大小: {path.stat().st_size} 字节", None
        except Exception as e:
            return f"读取文件时出错: {str(e)}", None
    
    # 预定义任务
    predefined_tasks = [
        "数据分析任务：对student_habits_performance.csv进行深度分析，生成包含可视化图表的报告",
        "混合研究任务：结合本地数据分析和网络研究，分析青少年睡眠与学业表现的关系", 
        "网络研究任务：搜索并分析最新的教育科技趋势报告",
        "数据可视化任务：为现有数据创建多种类型的可视化图表",
        "自定义任务：请在下方输入框中描述您的具体需求"
    ]
    
    # 创建界面
    with gr.Blocks(title="混合型智能代理 - 演示版", theme=gr.themes.Soft()) as demo:
        gr.Markdown("""
        # 🤖 混合型智能代理 (演示版)
        
        这是混合型智能代理的演示界面。完整版本需要安装所有依赖并配置API密钥。
        
        ⚠️ **当前为演示模式** - 显示模拟结果，不执行实际的AI分析
        """)
        
        with gr.Row():
            with gr.Column(scale=2):
                gr.Markdown("### 📋 任务配置")
                
                task_type = gr.Dropdown(
                    choices=predefined_tasks,
                    label="选择任务类型",
                    value=predefined_tasks[0]
                )
                
                custom_input = gr.Textbox(
                    label="自定义任务描述",
                    placeholder="请详细描述您的任务需求...",
                    lines=4,
                    visible=False
                )
                
                run_btn = gr.Button("🚀 运行演示", variant="primary", size="lg")
                
                status_output = gr.Textbox(
                    label="执行状态",
                    value="准备就绪 (演示模式)",
                    interactive=False
                )
            
            with gr.Column(scale=1):
                gr.Markdown("### 💡 使用说明")
                gr.Markdown("""
                **演示功能:**
                - 选择预定义任务类型
                - 查看模拟的执行结果
                - 预览生成文件的格式
                
                **完整功能需要:**
                1. 安装所有依赖: `pip install -r requirements.txt`
                2. 配置API密钥: 编辑 `.env` 文件
                3. 启动后端服务: SearxNG Docker
                4. 运行: `python start_gradio.py`
                """)
        
        gr.Markdown("---")
        
        with gr.Row():
            with gr.Column():
                gr.Markdown("### 📄 生成报告")
                report_output = gr.Textbox(
                    label="报告内容",
                    lines=15,
                    interactive=False,
                    placeholder="运行任务后，生成的报告将在这里显示..."
                )
            
            with gr.Column():
                gr.Markdown("### 📁 生成文件")
                files_output = gr.Markdown("暂无生成文件")
                
                file_selector = gr.Dropdown(
                    label="选择文件预览",
                    choices=get_demo_files(),
                    interactive=True
                )
                
                preview_btn = gr.Button("👁️ 预览文件", size="sm")
                
                file_preview = gr.Textbox(
                    label="文件内容",
                    lines=8,
                    interactive=False
                )
                
                image_preview = gr.Image(
                    label="图片预览",
                    visible=False
                )
        
        # 事件绑定
        task_type.change(
            fn=update_task_input,
            inputs=[task_type],
            outputs=[custom_input]
        )
        
        run_btn.click(
            fn=run_demo_task,
            inputs=[task_type, custom_input],
            outputs=[status_output, report_output, files_output]
        )
        
        preview_btn.click(
            fn=preview_demo_file,
            inputs=[file_selector],
            outputs=[file_preview, image_preview]
        )
        
        # 页面加载时更新文件列表
        demo.load(
            fn=get_demo_files,
            outputs=[file_selector]
        )
    
    return demo

if __name__ == "__main__":
    print("🚀 启动混合型智能代理演示界面...")
    print("📝 这是演示版本，显示模拟结果")
    print("🔧 完整功能请运行: python start_gradio.py")
    
    demo = create_demo_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True
    )
