import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# 读取数据
data = pd.read_csv('student_habits_performance.csv')

# 绘制散点图与回归线
plt.figure(figsize=(10, 6))
sns.regplot(x='social_media_hours', y='exam_score', data=data, scatter_kws={'alpha':0.5})
plt.title('社交媒体使用时长与考试成绩的关系', fontproperties='SimSun.ttf')
plt.xlabel('社交媒体使用时长（小时）', fontproperties='SimSun.ttf')
plt.ylabel('考试成绩', fontproperties='SimSun.ttf')
plt.grid(True)

# 保存图表
plt.savefig('sleep_social_vs_score.png')
plt.close()

print("图表已保存为 'sleep_social_vs_score.png'。")