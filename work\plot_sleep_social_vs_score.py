import pandas as pd
import matplotlib.pyplot as plt
import os

# Load the data
data = pd.read_csv('student_habits_performance.csv')

# Set the font for plotting
plt.rcParams['font.family'] = 'SimSun'

# Create a figure with two subplots
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))

# Plot sleep_hours vs exam_score
ax1.scatter(data['sleep_hours'], data['exam_score'], color='blue', alpha=0.5)
ax1.set_title('Sleep Hours vs Exam Score')
ax1.set_xlabel('Sleep Hours')
ax1.set_ylabel('Exam Score')

# Plot social_media_hours vs exam_score
ax2.scatter(data['social_media_hours'], data['exam_score'], color='red', alpha=0.5)
ax2.set_title('Social Media Hours vs Exam Score')
ax2.set_xlabel('Social Media Hours')
ax2.set_ylabel('Exam Score')

# Adjust layout and save the figure
plt.tight_layout()
plt.savefig('sleep_social_vs_score.png')
print("Scatter plots saved as 'sleep_social_vs_score.png'")
