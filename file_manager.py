# 文件路径: file_manager.py
# 文件管理组件，用于处理生成文件的展示和下载

import os
import base64
import mimetypes
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
from PIL import Image
import io

class FileManager:
    """文件管理器类，处理工作目录中的文件操作"""
    
    def __init__(self, work_dir: str = "work"):
        self.work_dir = Path(work_dir)
        self.work_dir.mkdir(exist_ok=True)
    
    def get_file_list(self) -> List[Dict[str, Any]]:
        """获取工作目录中的所有文件信息"""
        files = []
        
        if not self.work_dir.exists():
            return files
        
        for file_path in self.work_dir.rglob("*"):
            if file_path.is_file():
                try:
                    stat = file_path.stat()
                    file_info = {
                        "path": str(file_path),
                        "name": file_path.name,
                        "relative_path": str(file_path.relative_to(self.work_dir)),
                        "size": stat.st_size,
                        "size_human": self._format_size(stat.st_size),
                        "modified": stat.st_mtime,
                        "type": self._get_file_type(file_path),
                        "extension": file_path.suffix.lower(),
                        "mime_type": mimetypes.guess_type(str(file_path))[0] or "application/octet-stream"
                    }
                    files.append(file_info)
                except (OSError, PermissionError):
                    continue
        
        # 按修改时间排序（最新的在前）
        files.sort(key=lambda x: x["modified"], reverse=True)
        return files
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def _get_file_type(self, file_path: Path) -> str:
        """根据文件扩展名确定文件类型"""
        ext = file_path.suffix.lower()
        
        type_mapping = {
            # 图片文件
            '.png': 'image',
            '.jpg': 'image', 
            '.jpeg': 'image',
            '.gif': 'image',
            '.bmp': 'image',
            '.svg': 'image',
            '.webp': 'image',
            
            # 文本文件
            '.md': 'text',
            '.txt': 'text',
            '.log': 'text',
            '.json': 'text',
            '.yaml': 'text',
            '.yml': 'text',
            
            # 数据文件
            '.csv': 'data',
            '.xlsx': 'data',
            '.xls': 'data',
            '.parquet': 'data',
            
            # 代码文件
            '.py': 'code',
            '.js': 'code',
            '.html': 'code',
            '.css': 'code',
            '.sql': 'code',
            
            # 文档文件
            '.pdf': 'document',
            '.doc': 'document',
            '.docx': 'document',
            
            # 压缩文件
            '.zip': 'archive',
            '.tar': 'archive',
            '.gz': 'archive',
        }
        
        return type_mapping.get(ext, 'other')
    
    def read_file_content(self, file_path: str, max_size: int = 1024*1024) -> Tuple[str, bool]:
        """读取文件内容
        
        Returns:
            Tuple[str, bool]: (内容, 是否截断)
        """
        path = Path(file_path)
        
        if not path.exists():
            return "文件不存在", False
        
        if not path.is_file():
            return "不是文件", False
        
        file_type = self._get_file_type(path)
        
        try:
            if file_type == 'image':
                return self._handle_image_file(path)
            elif file_type in ['text', 'code']:
                return self._handle_text_file(path, max_size)
            elif file_type == 'data':
                return self._handle_data_file(path)
            else:
                return f"无法预览 {file_type} 类型的文件", False
                
        except Exception as e:
            return f"读取文件时出错: {str(e)}", False
    
    def _handle_image_file(self, path: Path) -> Tuple[str, bool]:
        """处理图片文件"""
        try:
            # 获取图片信息
            with Image.open(path) as img:
                width, height = img.size
                mode = img.mode
                format_name = img.format
            
            info = f"图片信息:\n"
            info += f"- 格式: {format_name}\n"
            info += f"- 尺寸: {width} x {height}\n"
            info += f"- 模式: {mode}\n"
            info += f"- 文件大小: {self._format_size(path.stat().st_size)}\n"
            info += f"\n图片路径: {path}"
            
            return info, False
            
        except Exception as e:
            return f"无法读取图片: {str(e)}", False
    
    def _handle_text_file(self, path: Path, max_size: int) -> Tuple[str, bool]:
        """处理文本文件"""
        try:
            file_size = path.stat().st_size
            
            if file_size > max_size:
                # 文件太大，只读取前面部分
                with open(path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read(max_size)
                return content, True
            else:
                with open(path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                return content, False
                
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(path, 'r', encoding='gbk', errors='ignore') as f:
                    content = f.read(max_size if path.stat().st_size > max_size else -1)
                return content, path.stat().st_size > max_size
            except:
                return "无法解码文件内容", False
    
    def _handle_data_file(self, path: Path) -> Tuple[str, bool]:
        """处理数据文件"""
        try:
            if path.suffix.lower() == '.csv':
                df = pd.read_csv(path)
                
                info = f"CSV文件信息:\n"
                info += f"- 行数: {len(df)}\n"
                info += f"- 列数: {len(df.columns)}\n"
                info += f"- 列名: {', '.join(df.columns.tolist())}\n"
                info += f"- 文件大小: {self._format_size(path.stat().st_size)}\n\n"
                
                # 显示前几行数据
                info += "数据预览 (前10行):\n"
                info += df.head(10).to_string(index=False)
                
                if len(df) > 10:
                    info += f"\n\n... 还有 {len(df) - 10} 行数据"
                
                return info, len(df) > 10
                
            elif path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(path)
                
                info = f"Excel文件信息:\n"
                info += f"- 行数: {len(df)}\n"
                info += f"- 列数: {len(df.columns)}\n"
                info += f"- 列名: {', '.join(df.columns.tolist())}\n"
                info += f"- 文件大小: {self._format_size(path.stat().st_size)}\n\n"
                
                info += "数据预览 (前5行):\n"
                info += df.head(5).to_string(index=False)
                
                return info, len(df) > 5
            else:
                return "不支持的数据文件格式", False
                
        except Exception as e:
            return f"读取数据文件时出错: {str(e)}", False
    
    def get_image_path(self, file_path: str) -> Optional[str]:
        """获取图片文件路径用于Gradio显示"""
        path = Path(file_path)
        
        if not path.exists() or self._get_file_type(path) != 'image':
            return None
        
        return str(path)
    
    def delete_file(self, file_path: str) -> bool:
        """删除文件"""
        try:
            path = Path(file_path)
            if path.exists() and path.is_file():
                path.unlink()
                return True
            return False
        except Exception:
            return False
    
    def create_file_summary(self) -> str:
        """创建文件摘要信息"""
        files = self.get_file_list()
        
        if not files:
            return "工作目录为空"
        
        # 按类型统计
        type_counts = {}
        total_size = 0
        
        for file_info in files:
            file_type = file_info['type']
            type_counts[file_type] = type_counts.get(file_type, 0) + 1
            total_size += file_info['size']
        
        summary = f"📁 工作目录摘要:\n"
        summary += f"- 总文件数: {len(files)}\n"
        summary += f"- 总大小: {self._format_size(total_size)}\n\n"
        
        summary += "📊 文件类型分布:\n"
        type_emojis = {
            'image': '🖼️',
            'text': '📄',
            'data': '📊',
            'code': '💻',
            'document': '📋',
            'archive': '📦',
            'other': '📁'
        }
        
        for file_type, count in sorted(type_counts.items()):
            emoji = type_emojis.get(file_type, '📁')
            summary += f"  {emoji} {file_type}: {count} 个文件\n"
        
        return summary
    
    def get_recent_files(self, limit: int = 5) -> List[Dict[str, Any]]:
        """获取最近修改的文件"""
        files = self.get_file_list()
        return files[:limit]
