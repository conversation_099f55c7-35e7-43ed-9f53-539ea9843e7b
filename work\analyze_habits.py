import pandas as pd
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimSun']
plt.rcParams['axes.unicode_minus'] = False

# 读取数据
data = pd.read_csv('student_habits_performance.csv')

# 提取习惯变量
habit_columns = [col for col in data.columns if 'habit' in col.lower()]

# 绘制箱线图
plt.figure(figsize=(12, 8))
for i, col in enumerate(habit_columns, 1):
    plt.subplot(2, 3, i)
    data.boxplot(column=col)
    plt.title(f'{col} 箱线图')
    plt.ylabel('值')

plt.tight_layout()
plt.savefig('habits_boxplot.png')
print('箱线图已保存为 habits_boxplot.png')

# 显示异常值
for col in habit_columns:
    q1 = data[col].quantile(0.25)
    q3 = data[col].quantile(0.75)
    iqr = q3 - q1
    lower_bound = q1 - 1.5 * iqr
    upper_bound = q3 + 1.5 * iqr
    outliers = data[(data[col] < lower_bound) | (data[col] > upper_bound)]
    print(f'{col} 异常值数量: {len(outliers)}')
    print(outliers[[col]])