import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimSun']
plt.rcParams['axes.unicode_minus'] = False

# 读取数据
data = pd.read_csv('student_habits_performance.csv')
print("数据读取完成，前5行数据如下：")
print(data.head())

# 仅选择数值型列进行相关性分析
numeric_data = data.select_dtypes(include=['float64', 'int64'])
print("\n数值型列如下：")
print(numeric_data.columns)

# 计算相关性矩阵
correlation_matrix = numeric_data.corr()
print("\n相关性矩阵如下：")
print(correlation_matrix)

# 可视化相关性矩阵
plt.figure(figsize=(10, 8))
sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)
plt.title('变量相关性热力图')
plt.savefig('correlation_heatmap.png')
print("\n相关性热力图已保存为 'correlation_heatmap.png'")

# 提取学习习惯与学业表现的相关性
study_habits = [col for col in numeric_data.columns if '学习习惯' in col]
performance = [col for col in numeric_data.columns if '学业表现' in col]

if study_habits and performance:
    study_performance_corr = correlation_matrix.loc[study_habits, performance]
    print("\n学习习惯与学业表现的相关性如下：")
    print(study_performance_corr)

    # 可视化学习习惯与学业表现的相关性
    plt.figure(figsize=(8, 6))
    sns.barplot(x=study_performance_corr.index, y=study_performance_corr.values.flatten())
    plt.title('学习习惯与学业表现相关性')
    plt.xticks(rotation=45)
    plt.savefig('study_performance_correlation.png')
    print("\n学习习惯与学业表现相关性图已保存为 'study_performance_correlation.png'")
else:
    print("\n未找到学习习惯或学业表现相关字段。")